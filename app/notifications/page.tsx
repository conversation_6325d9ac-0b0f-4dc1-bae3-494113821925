"use client";

import { useEffect, useState } from 'react';
import { useNotifications } from '@/app/contexts/NotificationsContext';
import { Notification, NotificationType } from '@/app/types/notifications';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { useRouter } from 'next/navigation';
import ClientLayout from '@/app/components/layout/ClientLayout';
import { BellIcon } from '@heroicons/react/24/outline';

export default function NotificationsPage() {
  const { notifications, markAsRead, markAllAsRead, unreadCount } = useNotifications();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);
  const [filterType, setFilterType] = useState<string | null>(null);

  // Evitar errores de hidratación
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <ClientLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Notificaciones</h1>
          <div className="animate-pulse space-y-4">
            <div className="h-16 bg-gray-100 rounded-lg"></div>
            <div className="h-16 bg-gray-100 rounded-lg"></div>
            <div className="h-16 bg-gray-100 rounded-lg"></div>
          </div>
        </div>
      </ClientLayout>
    );
  }

  const formatTime = (timestamp: number) => {
    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: es
      });
    } catch (error) {
      return 'fecha desconocida';
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);

    // Navegar según el tipo de notificación
    if (notification.type === NotificationType.SESSION_TODAY && notification.metadata?.sessionId) {
      router.push(`/calendar?sessionId=${notification.metadata.sessionId}`);
    } else if (notification.type === NotificationType.AI_CLIENT_MATCH) {
      // Redireccionar a la página de mensajes si hay un conversationId
      if (notification.metadata?.conversationId) {
        router.push(`/messages?conversation=${notification.metadata.conversationId}`);
      } else {
        // Si no hay conversationId, intentar usar el clientId como respaldo
        console.log('No se encontró conversationId en la notificación, usando clientId como respaldo');
        router.push(`/messages`);
      }
    }
  };

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case NotificationType.AI_CLIENT_MATCH:
        return (
          <div className="p-2 bg-purple-100 rounded-full mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case NotificationType.SESSION_TODAY:
        return (
          <div className="p-2 bg-blue-100 rounded-full mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="p-2 bg-gray-100 rounded-full mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  const getTypeName = (type: NotificationType): string => {
    switch (type) {
      case NotificationType.AI_CLIENT_MATCH:
        return 'IA: Cliente Potencial';
      case NotificationType.SESSION_TODAY:
        return 'Sesión Hoy';
      default:
        return 'General';
    }
  };

  const filteredNotifications = filterType 
    ? notifications.filter(notification => notification.type === filterType)
    : notifications;

  return (
    <ClientLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Notificaciones</h1>
          
          <div className="flex gap-4 items-center">
            {unreadCount > 0 && (
              <button 
                onClick={() => markAllAsRead()}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Marcar todas como leídas
              </button>
            )}
            
            <div className="flex items-center gap-2">
              <select 
                value={filterType || ''} 
                onChange={(e) => setFilterType(e.target.value || null)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">Todas</option>
                <option value={NotificationType.SESSION_TODAY}>Sesiones Hoy</option>
                <option value={NotificationType.AI_CLIENT_MATCH}>IA: Clientes Potenciales</option>
              </select>
            </div>
          </div>
        </div>
        
        {filteredNotifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16 text-gray-500">
            <BellIcon className="h-12 w-12 mb-4 text-gray-400" />
            <p className="text-lg mb-2">No hay notificaciones</p>
            <p className="text-sm text-gray-400">
              {filterType 
                ? 'No hay notificaciones con este filtro' 
                : 'Las notificaciones aparecerán aquí'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredNotifications.map((notification) => {
              const isUnread = !notification.read;
              
              return (
                <div 
                  key={notification.id} 
                  className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100"
                >
                  <button
                    onClick={() => handleNotificationClick(notification)}
                    className={`w-full text-left p-4 flex hover:bg-gray-50 transition-colors ${
                      isUnread ? 'bg-white' : 'bg-gray-50'
                    }`}
                  >
                    {getNotificationIcon(notification.type)}
                    
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className={`font-medium ${isUnread ? 'text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </h3>
                          
                          <span className="inline-block px-2 py-0.5 bg-gray-100 text-gray-600 rounded text-xs mt-1">
                            {getTypeName(notification.type)}
                          </span>
                        </div>
                        
                        <div className="flex items-center">
                          {isUnread && (
                            <span className="inline-block h-2 w-2 bg-blue-600 rounded-full mr-2"></span>
                          )}
                          <span className="text-sm text-gray-500">{formatTime(notification.createdAt)}</span>
                        </div>
                      </div>
                      
                      <p className={`mt-2 ${isUnread ? 'text-gray-700' : 'text-gray-500'}`}>
                        {notification.message}
                      </p>
                    </div>
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </ClientLayout>
  );
} 