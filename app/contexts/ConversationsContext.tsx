'use client';

import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { database as rtdb } from '@/lib/firebase/config';

import { db } from '@/lib/firebase/config';
import { ref, onValue, push, update, get, serverTimestamp, query as dbQuery, orderByChild, equalTo, limitToLast, set } from 'firebase/database';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
// Importamos la función para procesar imágenes de perfil
import { processAndSaveProfilePicture } from '../services/unipile-profile-image';
import { uploadImage } from '@/lib/cloudinary/config';
import { syncProfileImages } from '../services/profile-image-sync';
import { Conversation, ConversationMessage, Platform } from '@/app/types/conversations';
import { toast } from 'react-hot-toast';
import { sendTextMessage as sendSinchTextMessage, sendMediaMessage as sendSinchMediaMessage, sendMultipleMediaMessages as sendSinchMultipleMediaMessages } from '@/app/services/sinch-sender';
import { sendTextMessage as sendUnipileTextMessage, sendMediaMessage as sendUnipileMediaMessage, sendMessageWithAttachments as sendUnipileMessageWithAttachments } from '@/app/services/unipile-sender';

interface MetaConnection {
  id: string;
  pageId: string;
  pageAccessToken: string;
  platform: Platform;
  userId: string;
  pageName?: string;
}

interface ConversationsContextType {
  conversations: Conversation[];
  selectedConversation: Conversation | null;
  messages: ConversationMessage[];
  loading: boolean;
  error: string | null;
  loadingMessages: boolean;
  selectConversation: (conversation: Conversation | null) => Promise<void>;
  sendMessage: (content: string, attachments?: File[]) => Promise<void>;
  loadMoreMessages: () => Promise<void>;
  refreshConversations: () => Promise<void>;
  fetchConversations: () => Promise<void>;
  markAsRead: (conversationId: string) => Promise<void>;
  linkClientToConversation: (conversationId: string, clientId: string) => Promise<void>;
  unreadMessagesCount: number;
}

const ConversationsContext = createContext<ConversationsContextType | undefined>(undefined);

export function ConversationsProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [metaConnections, setMetaConnections] = useState<MetaConnection[]>([]);
  const messagesLimit = useRef(100);
  const unsubscribeMessages = useRef<(() => void) | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);

  // Función para cargar conexiones de Meta
  const loadMetaConnections = useCallback(async () => {
    try {
      if (!user?.uid) {
        console.log('⚠️ No hay usuario autenticado para cargar conexiones Meta');
        setMetaConnections([]);
        return;
      }

      console.log('🔍 Buscando conexiones Meta para usuario:', user.uid);

      // Obtener las conexiones de Meta para este usuario
      const metaConnectionsRef = collection(db, 'metaConnections');
      const q = query(metaConnectionsRef, where('userId', '==', user.uid));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        console.log('📭 No se encontraron conexiones Meta para el usuario');
        setMetaConnections([]);
      } else {
        // Mapear los documentos a un formato utilizable
        const connections: MetaConnection[] = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          pageId: doc.data().pageId,
          pageAccessToken: doc.data().pageAccessToken,
          platform: doc.data().platform,
          userId: doc.data().userId,
          pageName: doc.data().pageName
        }));

        console.log(`✅ Encontradas ${connections.length} conexiones Meta:`, connections.map(c => c.pageId));

        // Comparar si las conexiones han cambiado realmente antes de actualizar el estado
        const connectionsChanged =
          JSON.stringify(connections.map(c => c.id)) !==
          JSON.stringify(metaConnections.map(c => c.id));

        if (connectionsChanged) {
          setMetaConnections(connections);
        }
      }
    } catch (error) {
      console.error('❌ Error al cargar conexiones Meta:', error);
      // En caso de error, establecemos un array vacío para evitar problemas
      setMetaConnections([]);
    }
  }, [user?.uid, db]);

  // Función para marcar una conversación como leída
  const markAsRead = useCallback(async (conversationId: string) => {
    // Al marcar como leída, actualizar el contador de mensajes no leídos
    const updatedUnreadCount = conversations.reduce((total, conv) => {
      if (conv.id === conversationId) {
        return total; // Esta conversación será marcada como leída
      }
      return total + (conv.unreadCount || 0);
    }, 0);
    
    setUnreadMessagesCount(updatedUnreadCount);
    
    // Si no hay mensajes no leídos, resetear el título
    if (updatedUnreadCount === 0) {
      resetNotifications();
    } else {
      updatePageTitle(updatedUnreadCount);
    }
    if (!user?.uid) return;
    try {
      // Actualizar en RTDB
      const conversationRef = ref(rtdb, `conversations/${user.uid}/${conversationId}`);
      await update(conversationRef, {
        unread: false,
        unreadCount: 0
      });

      // Actualizar también el estado local inmediatamente para una respuesta más rápida
      setConversations(prevConversations => {
        return prevConversations.map(conv => {
          if (conv.id === conversationId) {
            return {
              ...conv,
              unread: false,
              unreadCount: 0
            };
          }
          return conv;
        });
      });

      // Si es la conversación seleccionada, actualizar también ese estado
      if (selectedConversation?.id === conversationId) {
        setSelectedConversation(prev => {
          if (prev) {
            return {
              ...prev,
              unread: false,
              unreadCount: 0
            };
          }
          return prev;
        });
      }

      console.log(`✅ Conversación ${conversationId} marcada como leída`);
    } catch (err) {
      console.error('Error marking conversation as read:', err);
    }
  }, [user, selectedConversation]);

  // Función para cargar mensajes
  const loadMessages = useCallback(async () => {
    if (!selectedConversation?.id) {
      console.log('⚠️ No hay conversación seleccionada o la conversación no tiene ID');
      // Desactivar estados de carga si no hay conversación seleccionada
      setLoadingMessages(false);
      return;
    }

    try {
      console.log('📩 Cargando mensajes para conversación:', selectedConversation.id);

      setLoadingMessages(true);
      setError(null);

      // Desuscribirse de mensajes anteriores si existen
      if (unsubscribeMessages.current) {
        console.log('🔄 Desuscribiendo de listener anterior de mensajes');
        unsubscribeMessages.current();
        unsubscribeMessages.current = null;
      }

      const messagesRef = ref(rtdb, `messages/${selectedConversation.id}`);

      console.log('📩 Configurando listener para mensajes en:', `messages/${selectedConversation.id}`);

      // Usar una variable para almacenar los mensajes actuales y evitar actualizaciones innecesarias
      let currentMessages: ConversationMessage[] = [];

      const unsubscribe = onValue(messagesRef, (snapshot) => {
        try {
          if (snapshot.exists()) {
            const messagesData = snapshot.val();
            const messagesList = Object.entries(messagesData).map(([id, data]: [string, any]) => ({
              id,
              ...data
            })).sort((a, b) => b.timestamp - a.timestamp);

            console.log(`📬 Recibidos ${messagesList.length} mensajes`);

            // Comparar si los mensajes han cambiado realmente antes de actualizar el estado
            const messagesChanged = messagesList.length !== currentMessages.length ||
              JSON.stringify(messagesList.map(m => m.id)) !== JSON.stringify(currentMessages.map(m => m.id));

            if (messagesChanged) {
              currentMessages = messagesList;
              setMessages(messagesList);
            }
          } else {
            console.log('📭 No hay mensajes para esta conversación');
            currentMessages = [];
            setMessages([]);
          }
        } catch (error) {
          console.error('Error processing messages data:', error);
          currentMessages = [];
          setMessages([]);
          setError('Error processing messages data');
        } finally {
          // Siempre desactivar el estado de carga después de procesar los datos
          setLoadingMessages(false);
        }
      }, (error) => {
        console.error('❌ Error al cargar mensajes:', error);
        setError('Error loading messages');
        setLoadingMessages(false);
      });

      unsubscribeMessages.current = unsubscribe;
    } catch (err) {
      console.error('❌ Error loading messages:', err);
      setError('Error loading messages');
      setLoadingMessages(false);
    }
  }, [selectedConversation, rtdb]);

  // Función para cargar conversaciones
  const loadConversations = useCallback(async () => {
    if (!user?.uid) {
      console.log('No user or user.uid found for loading conversations:', user);
      // Asegurar que loading es false incluso si no hay usuario
      setLoading(false);
      return () => {};
    }

    try {
      console.log('🔄 Cargando conversaciones para usuario:', user.uid);

      setError(null);

      // Obtener todos los IDs de páginas de Meta si existen
      const pageIds = metaConnections.map(conn => conn.pageId);
      console.log('Loading conversations for pageIds:', pageIds);

      // Desactivar el modo de presentación para usar conversaciones reales
      const useDemoMode = false; // Cambiar a false para desactivar las conversaciones de demostración

      if (useDemoMode) {
        console.log('🎭 MODO PRESENTACIÓN ACTIVADO: Creando conversaciones falsas para demo');
        // Código de demo eliminado para evitar confusiones
      } else {
        // Usar conversaciones reales de la base de datos
        console.log('🔄 Usando conversaciones reales de la base de datos');
      }

      // Referencia a las conversaciones del usuario en la base de datos
      const conversationsRef = ref(rtdb, `conversations/${user.uid}`);

      // Usar una variable para almacenar las conversaciones actuales y evitar actualizaciones innecesarias
      let currentConversations: Conversation[] = [...conversations]; // Inicializar con las conversaciones actuales

      const unsubscribe = onValue(conversationsRef, (snapshot) => {
        try {
          if (snapshot.exists()) {
            const conversationsData = snapshot.val();

            // Log detallado de la estructura de datos de RTDB
            console.log('💾 [RTDB Debug] Estructura completa de datos:', JSON.stringify(conversationsData, null, 2));

            // Examinar la primera conversación para ver su estructura
            const firstConvId = Object.keys(conversationsData)[0];
            if (firstConvId) {
              const firstConv = conversationsData[firstConvId];
              console.log('💾 [RTDB Debug] Estructura de la primera conversación:', {
                id: firstConvId,
                hasParticipant: !!firstConv.participant,
                participantStructure: firstConv.participant ? Object.keys(firstConv.participant) : 'N/A',
                hasProfilePictureUrl: !!firstConv.profilePictureUrl,
                hasParticipantPicture: !!firstConv.participantPicture,
                profilePictureUrl: firstConv.profilePictureUrl,
                participantPicture: firstConv.participantPicture,
                participantProfilePic: firstConv.participant?.profilePic
              });
            }

            // Filtrar entradas inválidas (como booleanos) y procesar solo objetos válidos
            let conversationsList = Object.entries(conversationsData)
              .filter(([id, data]) => {
                // Filtrar entradas que no son objetos o que son booleanos
                return id !== 'unread' && id !== 'unreadCount' && typeof data === 'object' && data !== null;
              })
              .map(([id, data]: [string, any]) => {
                // Asegurarnos de que participant sea un objeto si no existe
                const participant = data.participant || {};

                // CORRECCIÓN: Mapear explícitamente los campos importantes para evitar conflictos
                const conversation: Conversation = {
                  id,
                  pageId: data.pageId || '',
                  platform: data.platform || 'instagram',
                  lastMessage: data.lastMessage || null,
                  lastMessageAt: data.lastMessageAt || 0,
                  unread: data.unread || false,
                  unreadCount: data.unreadCount || 0,
                  clientId: data.clientId || null,
                  participantId: data.participantId || null,
                  metaSenderId: data.metaSenderId || null,
                  superchatContactId: data.superchatContactId || null,
                  participantName: data.participantName || null,
                  isSuperchat: data.isSuperchat || false,
                  provider: data.provider || null,
                  attendeeId: data.attendeeId || null,
                  superchatChannelId: data.superchatChannelId || null,
                  
                  // CAMPOS DE IMAGEN - Mapear explícitamente para evitar conflictos
                  profilePictureUrl: data.profilePictureUrl || null,
                  participantPicture: data.participantPicture || null,
                  profilePictureUpdatedAt: data.profilePictureUpdatedAt || null,
                  lastImageUpdate: data.lastImageUpdate || null,
                  profilePictureSuccess: data.profilePictureSuccess || null,
                  profilePictureError: data.profilePictureError || null,
                  profilePictureAttempts: data.profilePictureAttempts || null,
                  lastProfileAttempt: data.lastProfileAttempt || null,
                  
                  // CAMPOS DE IMAGEN (formato antiguo) - Importados desde Firebase
                  profilePicUrl: data.profilePicUrl || null,
                  profilePicUpdatedAt: data.profilePicUpdatedAt || null,
                  
                  // PARTICIPANT - Mantener estructura original
                  participant: {
                    id: participant.id || '',
                    name: participant.name || '',
                    profilePic: participant.profilePic || null,
                    profilePictureUrl: participant.profilePictureUrl || null
                  },
                  
                  // Otros campos que puedan existir
                  ...Object.fromEntries(
                    Object.entries(data).filter(([key]) => 
                      !['id', 'pageId', 'platform', 'lastMessage', 'lastMessageAt', 'unread', 'unreadCount', 
                        'clientId', 'participantId', 'metaSenderId', 'superchatContactId', 'participantName', 
                        'isSuperchat', 'provider', 'attendeeId', 'superchatChannelId', 'profilePictureUrl', 
                        'participantPicture', 'profilePictureUpdatedAt', 'lastImageUpdate', 'profilePictureSuccess', 
                        'profilePictureError', 'profilePictureAttempts', 'lastProfileAttempt', 'participant',
                        'profilePicUrl', 'profilePicUpdatedAt'].includes(key)
                    )
                  )
                };

                // Log específico para conversaciones de Instagram
                if (conversation.platform === 'instagram') {
                  console.log(`📸 [Instagram Mapping] ${conversation.id} (${conversation.participantName}):`, {
                    profilePictureUrl: conversation.profilePictureUrl,
                    profilePictureUpdatedAt: conversation.profilePictureUpdatedAt,
                    lastImageUpdate: conversation.lastImageUpdate,
                    participantPicture: conversation.participantPicture,
                    participantProfilePic: conversation.participant?.profilePic
                  });
                }

                return conversation;
              });

            console.log('💾 [RTDB Debug] Conversaciones procesadas:', conversationsList.length);

            // Ordenar por último mensaje
            conversationsList = conversationsList.sort((a, b) => (b.lastMessageAt || 0) - (a.lastMessageAt || 0));

            console.log('Final conversations list count:', conversationsList.length);

            // Siempre actualizar el estado para asegurar que tenemos los datos más recientes
            // Esto garantiza que se muestren los últimos mensajes y el orden correcto
            console.log('🔄 Actualizando lista de conversaciones en tiempo real');
            currentConversations = conversationsList;
            setConversations(conversationsList);
          } else {
            console.log('No conversations found in database');
            if (currentConversations.length > 0) {
              currentConversations = [];
              setConversations([]);
            }
          }
        } catch (error) {
          console.error('Error processing conversations data:', error);
          currentConversations = [];
          setConversations([]);
          setError('Error processing conversations data');
        } finally {
          // Siempre desactivar el estado de carga después de procesar los datos
          setLoading(false);

          // Sincronizar imágenes de perfil desde Firestore a RTDB
          if (user?.uid) {
            // Configurar un listener para actualizaciones de imágenes de perfil
            try {
              const profilePicturesRef = ref(rtdb, `profilePictures/${user.uid}`);

              // Usar onValue para escuchar cambios en las imágenes de perfil
              onValue(profilePicturesRef, (snapshot) => {
                if (snapshot.exists()) {
                  const profilePictures = snapshot.val();
                  console.log('🖼️ Actualizando imágenes de perfil en tiempo real:', Object.keys(profilePictures).length);

                  // Actualizar las conversaciones con las nuevas imágenes
                  setConversations(prevConversations => {
                    const updatedConversations = prevConversations.map(conv => {
                      // Si hay una imagen para esta conversación, actualizarla
                      if (profilePictures[conv.id]) {
                        return {
                          ...conv,
                          profilePictureUrl: profilePictures[conv.id].url,
                          participantPicture: profilePictures[conv.id].url,
                          participant: conv.participant ? {
                            ...conv.participant,
                            profilePic: profilePictures[conv.id].url,
                            profilePictureUrl: profilePictures[conv.id].url
                          } : undefined
                        };
                      }
                      return conv;
                    });

                    return updatedConversations;
                  });

                  // Actualizar también la conversación seleccionada si corresponde
                  if (selectedConversation && profilePictures[selectedConversation.id]) {
                    setSelectedConversation(prev => {
                      if (prev) {
                        return {
                          ...prev,
                          profilePictureUrl: profilePictures[prev.id].url,
                          participantPicture: profilePictures[prev.id].url,
                          participant: prev.participant ? {
                            ...prev.participant,
                            profilePic: profilePictures[prev.id].url,
                            profilePictureUrl: profilePictures[prev.id].url
                          } : undefined
                        };
                      }
                      return prev;
                    });
                  }
                }
              });
            } catch (profilePicturesError) {
              console.error('❌ Error al configurar listener de imágenes de perfil:', profilePicturesError);
            }

            // También ejecutar la sincronización inicial
            syncProfileImages(user.uid)
              .then(() => {
                console.log('✅ Sincronización inicial de imágenes de perfil completada');
              })
              .catch((syncError) => {
                console.error('❌ Error en sincronización inicial de imágenes de perfil:', syncError);
              });
          }
        }
      }, (error) => {
        console.error('Error loading conversations:', error);
        setError('Error loading conversations');
        setLoading(false);
      });

      return unsubscribe;
    } catch (err) {
      console.error('Error setting up conversations listener:', err);
      setError('Error loading conversations');
      setLoading(false);
      return () => {};
    }
  }, [user?.uid, rtdb, metaConnections, conversations]);

  // Función para crear mensajes de demostración
  const createDemoMessages = (conversationId: string) => {
    console.log('🎭 Creando mensajes de demostración para conversación:', conversationId);

    const demoMessages: ConversationMessage[] = [
      {
        id: 'msg1',
        content: 'Hola, me gustaría información sobre sus servicios de tatuajes',
        timestamp: Date.now() - 1000 * 60 * 60, // 1 hora atrás
        sender: 'user',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg2',
        content: '¡Hola! Claro, ofrecemos varios estilos de tatuajes. ¿Qué estilo te interesa?',
        timestamp: Date.now() - 1000 * 60 * 55, // 55 minutos atrás
        sender: 'business',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg3',
        content: 'Me interesan los tatuajes minimalistas',
        timestamp: Date.now() - 1000 * 60 * 50, // 50 minutos atrás
        sender: 'user',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg4',
        content: 'Perfecto, tenemos mucha experiencia en ese estilo. Te comparto algunos ejemplos de nuestro trabajo',
        timestamp: Date.now() - 1000 * 60 * 45, // 45 minutos atrás
        sender: 'business',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg5',
        content: '',
        timestamp: Date.now() - 1000 * 60 * 44, // 44 minutos atrás
        sender: 'business',
        read: true,
        attachments: ['https://res.cloudinary.com/dm3ue83hl/image/upload/v1741437500/demo/tattoo1.jpg'],
        platform: 'facebook'
      },
      {
        id: 'msg6',
        content: '',
        timestamp: Date.now() - 1000 * 60 * 43, // 43 minutos atrás
        sender: 'business',
        read: true,
        attachments: ['https://res.cloudinary.com/dm3ue83hl/image/upload/v1741437500/demo/tattoo2.jpg'],
        platform: 'facebook'
      },
      {
        id: 'msg7',
        content: 'Me encantan, ¿cuál sería el precio aproximado?',
        timestamp: Date.now() - 1000 * 60 * 40, // 40 minutos atrás
        sender: 'user',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg8',
        content: 'Los precios para tatuajes minimalistas comienzan desde $50.000 dependiendo del tamaño y complejidad',
        timestamp: Date.now() - 1000 * 60 * 35, // 35 minutos atrás
        sender: 'business',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg9',
        content: 'Genial, me gustaría agendar una consulta',
        timestamp: Date.now() - 1000 * 60 * 30, // 30 minutos atrás
        sender: 'user',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg10',
        content: 'Claro, tenemos disponibilidad para la próxima semana. ¿Qué día te vendría mejor?',
        timestamp: Date.now() - 1000 * 60 * 25, // 25 minutos atrás
        sender: 'business',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg11',
        content: 'El martes por la tarde estaría perfecto',
        timestamp: Date.now() - 1000 * 60 * 20, // 20 minutos atrás
        sender: 'user',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg12',
        content: 'Excelente, te agendamos para el martes a las 16:00. ¿Te parece bien?',
        timestamp: Date.now() - 1000 * 60 * 15, // 15 minutos atrás
        sender: 'business',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg13',
        content: 'Perfecto, ahí estaré. ¿Necesito llevar algo?',
        timestamp: Date.now() - 1000 * 60 * 10, // 10 minutos atrás
        sender: 'user',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg14',
        content: 'Solo trae tus ideas y referencias si tienes alguna. ¡Nos vemos el martes!',
        timestamp: Date.now() - 1000 * 60 * 5, // 5 minutos atrás
        sender: 'business',
        read: true,
        platform: 'facebook'
      },
      {
        id: 'msg15',
        content: 'Me gustaría agendar una sesión para un tatuaje pequeño',
        timestamp: Date.now() - 1000 * 60 * 1, // 1 minuto atrás
        sender: 'user',
        read: false,
        platform: 'facebook'
      }
    ];

    setMessages(demoMessages);
  };

  // Función para seleccionar conversación
  const selectConversation = async (
    conversation: Conversation | null,
    onSuccess?: () => void
  ) => {
    try {
      console.log('📱 Seleccionando conversación:', conversation?.id || 'null/sin ID');

      // Si la conversación es null, limpiamos la selección actual
      if (!conversation) {
        console.log('🔄 Limpiando selección de conversación (null)');
        setSelectedConversation(null);
        setMessages([]);
        if (unsubscribeMessages.current) {
          unsubscribeMessages.current();
          unsubscribeMessages.current = null;
        }
        return;
      }

      // Si no hay ID de conversación, no hacemos nada
      if (!conversation.id) {
        console.log('⏭️ Sin ID de conversación, omitiendo selección');
        return;
      }

      if (selectedConversation?.id === conversation.id) {
        console.log('⏭️ Misma conversación ya seleccionada, omitiendo selección');
        return;
      }

      // Primero actualizamos el estado de la conversación seleccionada antes de cargar mensajes
      setSelectedConversation(conversation);

      // Activamos el estado de carga
      setLoading(true);
      setLoadingMessages(true);
      setError(null);

      // Intentar obtener la imagen de perfil si es una conversación de Unipile
      // Solo lo hacemos en el cliente, no durante la compilación
      if (typeof window !== 'undefined' && conversation.provider === 'sinch' && conversation.attendeeId && !conversation.profilePictureUrl) {
        console.log('🖼️ Intentando obtener imagen de perfil para attendeeId:', conversation.attendeeId);

        // Usamos setTimeout para evitar bloquear el hilo principal
        setTimeout(() => {
          try {
            // Obtener la imagen de perfil de forma asíncrona (no esperamos a que termine)
            processAndSaveProfilePicture(
              conversation.attendeeId || '', // Asegurarnos de que nunca sea undefined
              conversation.id,
              conversation.clientId
            ).then(imageUrl => {
              if (imageUrl) {
                console.log('✅ Imagen de perfil obtenida y guardada:', imageUrl);
                // Actualizar la conversación en memoria con la URL de la imagen
                setSelectedConversation(prev => {
                  if (prev && prev.id === conversation.id) {
                    return { ...prev, profilePictureUrl: imageUrl };
                  }
                  return prev;
                });
              }
            }).catch(error => {
              console.error('❌ Error al obtener imagen de perfil:', error);
            });
          } catch (error) {
            console.error('❌ Error al iniciar obtención de imagen de perfil:', error);
            // No interrumpimos el flujo principal si falla la obtención de la imagen
          }
        }, 1000); // Retrasar 1 segundo para no bloquear la UI
      }

      // Limpiar cualquier suscripción anterior a mensajes
      if (unsubscribeMessages.current) {
        console.log('🔄 Desuscribiendo de listener anterior de mensajes');
        unsubscribeMessages.current();
        unsubscribeMessages.current = null;
      }

      console.log('🔄 Cargando mensajes para conversación:', conversation.id);

      // Verificar que el ID de conversación tiene el formato correcto
      // Solo validar formato para conversaciones de Meta
      const validateConversationId = () => {
        // Si no tiene un proveedor explícito o no es de Meta, no validamos el formato
        if (!conversation.platform ||
            !['facebook', 'instagram', 'whatsapp'].includes(conversation.platform) ||
            conversation.provider === 'sinch' ||
            conversation.isSuperchat) {
          console.log('📱 Conversación no Meta o Sinch, omitiendo validación de formato de ID');
          return;
        }

        // Solo para conversaciones explícitamente de Meta, verificar formato pageId_recipientId
        if (conversation.platform === 'facebook' || conversation.platform === 'instagram') {
          const idParts = conversation.id.split('_');
          if (idParts.length < 2) {
            console.warn('⚠️ ID de conversación Meta con formato inusual:', conversation.id);
            // No lanzar error, solo registrar advertencia
            // throw new Error('Formato de ID de conversación inválido. Se esperaba: pageId_recipientId');
          } else {
            const [pageId, recipientId] = idParts;
            console.log('Extracted Meta IDs:', { pageId, recipientId });
          }
        }
      };

      try {
        // Validar el ID según proveedor
        validateConversationId();

        // Esta función carga los mensajes una sola vez para mostrarlos inmediatamente,
        // luego se establece un listener para actualizaciones en tiempo real
        const singleLoadMessagesPromise = new Promise<void>((resolve, reject) => {
          try {
            // Usar Realtime Database para cargar mensajes
            const messagesRef = ref(rtdb, `messages/${conversation.id}`);

            // Primero hacemos una carga única
            get(messagesRef).then((snapshot) => {
              try {
                if (snapshot.exists()) {
                  const messagesData = snapshot.val();
                  const messagesList = Object.entries(messagesData).map(([id, data]: [string, any]) => ({
                    id,
                    ...data
                  })).sort((a, b) => a.timestamp - b.timestamp); // Ordenar cronológicamente

                  console.log(`📬 Cargados ${messagesList.length} mensajes inicialmente`);
                  setMessages(messagesList);
                } else {
                  console.log('📭 No hay mensajes para esta conversación');
                  setMessages([]);
                }

                resolve(); // Resolvcemos la promesa independientemente del resultado
              } catch (error) {
                console.error('❌ Error al procesar datos de mensajes en carga inicial:', error);
                setMessages([]);
                setError('Error al procesar datos de mensajes');
                reject(error);
              }
            }).catch((error) => {
              console.error('❌ Error al cargar mensajes de RTDB en carga inicial:', error);
              setError('Error al cargar mensajes');
              reject(error);
            });

            // Luego configuramos un listener para cambios
            const unsubscribe = onValue(messagesRef, (snapshot) => {
              try {
                if (snapshot.exists()) {
                  const messagesData = snapshot.val();
                  const messagesList = Object.entries(messagesData).map(([id, data]: [string, any]) => ({
                    id,
                    ...data
                  })).sort((a, b) => a.timestamp - b.timestamp); // Ordenar cronológicamente

                  console.log(`📬 Actualizado a ${messagesList.length} mensajes`);
                  setMessages(messagesList);

                  // Actualizar también la conversación seleccionada con el último mensaje
                  if (messagesList.length > 0) {
                    const lastMessage = messagesList[messagesList.length - 1];
                    setSelectedConversation(prev => {
                      if (prev) {
                        return {
                          ...prev,
                          lastMessage: {
                            content: lastMessage.content,
                            timestamp: lastMessage.timestamp
                          },
                          lastMessageAt: lastMessage.timestamp
                        };
                      }
                      return prev;
                    });

                    // Actualizar también la lista de conversaciones para reflejar el último mensaje
                    setConversations(prevConversations => {
                      const updatedConversations = prevConversations.map(conv => {
                        if (conv.id === conversation.id) {
                          return {
                            ...conv,
                            lastMessage: {
                              content: lastMessage.content,
                              timestamp: lastMessage.timestamp
                            },
                            lastMessageAt: lastMessage.timestamp
                          };
                        }
                        return conv;
                      });

                      // Reordenar las conversaciones por último mensaje
                      return updatedConversations.sort((a, b) => (b.lastMessageAt || 0) - (a.lastMessageAt || 0));
                    });
                  }
                } else {
                  console.log('📭 No hay mensajes para esta conversación (actualización)');
                  setMessages([]);
                }
              } catch (error) {
                console.error('❌ Error al procesar datos de mensajes en tiempo real:', error);
                // No cambiamos el estado de mensajes si falla la actualización
              }
            });

            // Guardar la referencia para desuscribirse después
            unsubscribeMessages.current = unsubscribe;
          } catch (listenerError) {
            console.error('❌ Error al configurar listener de mensajes:', listenerError);
            reject(listenerError);
          }
        });

        // Esperar a que se carguen los mensajes y luego desactivar los estados de carga
        await singleLoadMessagesPromise;

        if (onSuccess) onSuccess();
      } catch (loadError) {
        console.error('❌ Error al cargar mensajes:', loadError);
        setError('Error al cargar mensajes');
        setMessages([]);
      } finally {
        // Asegurar que los estados de carga se desactiven
        setLoadingMessages(false);
        setLoading(false);
      }
    } catch (error) {
      console.error('❌ Error global al seleccionar conversación:', error);
      setError('Error al seleccionar conversación');
      // Limpiar estados
      setLoadingMessages(false);
      setLoading(false);
      setMessages([]);
    }
  };

  const refreshConversations = useCallback(async () => {
    if (!user?.uid) return;
    await loadConversations();
  }, [user, loadConversations]);

  // Función para enviar mensaje
  const sendMessage = useCallback(async (content: string, attachments?: File[]) => {
    if (!selectedConversation) return;
    if (!content.trim() && (!attachments || attachments.length === 0)) return;

    setIsSending(true);

    try {
      console.log('💬 Enviando mensaje a conversación:', selectedConversation.id);
      console.log('Proveedor:', selectedConversation.provider || 'default');

      let attachmentUrls: string[] = [];

      // Subir archivos adjuntos si hay
      if (attachments && attachments.length > 0) {
        console.log(`📎 Subiendo ${attachments.length} archivos adjuntos...`);
        try {
          // Subir cada archivo
          const uploadPromises = attachments.map(file => uploadImage(file));
          attachmentUrls = await Promise.all(uploadPromises);
          console.log('🔗 URLs de archivos subidos:', attachmentUrls);
        } catch (uploadError) {
          console.error('❌ Error al subir archivos:', uploadError);
          toast.error('Error al subir los archivos adjuntos');
          setIsSending(false);
          return;
        }
      }

      // Determinar qué proveedor usar para enviar mensajes
      try {
        // Verificar si la conversación es de Unipile
        const isUnipileConversation = selectedConversation.provider === 'sinch' ||
                                     selectedConversation.accountId !== undefined;

        console.log('🔍 Información de la conversación:', {
          id: selectedConversation.id,
          provider: selectedConversation.provider,
          accountId: selectedConversation.accountId,
          isUnipileConversation
        });

        if (isUnipileConversation) {
          console.log('📱 Usando Unipile para enviar mensaje');

          // Si hay archivos adjuntos, los enviamos directamente sin subirlos a Cloudinary
          if (attachments && attachments.length > 0) {
            // Enviar mensaje con texto y archivos adjuntos en una sola llamada
            await sendUnipileMessageWithAttachments(
              selectedConversation.id,
              content,
              attachments,
              user?.uid || ''
            );
          } else if (content.trim()) {
            // Enviar solo texto
            await sendUnipileTextMessage(
              selectedConversation.id,
              content,
              user?.uid || ''
            );
          }

          console.log('✅ Mensaje enviado correctamente a través de Unipile');
        } else {
          // Usar Sinch para otros proveedores (legacy)
          console.log('📱 Usando Sinch para enviar mensaje (legacy)');

          // Primero enviamos el mensaje de texto si existe
          if (content.trim()) {
            await sendSinchTextMessage(
              selectedConversation.id,
              content,
              user?.uid || ''
            );
          }

          // Luego enviamos cada adjunto como un mensaje separado
          if (attachmentUrls.length > 0) {
            await sendSinchMultipleMediaMessages(
              selectedConversation.id,
              attachmentUrls,
              user?.uid || ''
            );
          }

          console.log('✅ Mensaje enviado correctamente a través de Sinch');
        }

        // Actualizar la interfaz inmediatamente para mostrar el mensaje enviado
        // Esto proporciona retroalimentación instantánea al usuario
        const now = Date.now();

        // Crear un nuevo mensaje local para mostrar inmediatamente
        const newLocalMessage = {
          id: `local_${now}_${Math.random().toString(36).substring(2, 9)}`,
          content: content || '',
          timestamp: now,
          sender: 'business', // Importante: asignar 'business' para que se muestre a la derecha
          read: true,
          attachments: attachmentUrls.length > 0 ? attachmentUrls.map(url => ({ url })) : undefined
        };

        // Añadir el mensaje local a la lista de mensajes
        setMessages(prevMessages => [...prevMessages, newLocalMessage]);

        // Actualizar la conversación seleccionada con el último mensaje
        setSelectedConversation(prev => {
          if (prev) {
            return {
              ...prev,
              lastMessage: {
                content: content || (attachmentUrls.length > 0 ? 'Imagen' : ''),
                timestamp: now,
                sender: 'business' // Asegurarnos de que el sender sea 'business'
              },
              lastMessageAt: now
            };
          }
          return prev;
        });

        // Actualizar la lista de conversaciones para reflejar el último mensaje
        setConversations(prevConversations => {
          const updatedConversations = prevConversations.map(conv => {
            if (conv.id === selectedConversation.id) {
              return {
                ...conv,
                lastMessage: {
                  content: content || (attachmentUrls.length > 0 ? 'Imagen' : ''),
                  timestamp: now,
                  sender: 'business' // Asegurarnos de que el sender sea 'business'
                },
                lastMessageAt: now
              };
            }
            return conv;
          });

          // Reordenar las conversaciones por último mensaje
          return updatedConversations.sort((a, b) => (b.lastMessageAt || 0) - (a.lastMessageAt || 0));
        });

        console.log('✅ Interfaz actualizada con el mensaje enviado');
      } catch (error) {
        console.error('❌ Error al enviar mensaje a través de Sinch:', error);
        toast.error('Error al enviar mensaje');
        setIsSending(false);
        return;
      }

    } catch (error) {
      console.error('Error al enviar mensaje:', error);
      toast.error('Error al enviar mensaje');
    } finally {
      setIsSending(false);
    }
  }, [selectedConversation, user]);

  // Función para cargar más mensajes
  const loadMoreMessages = useCallback(async () => {
    if (!selectedConversation) return;

    try {
      messagesLimit.current += 100;
      await loadMessages();
    } catch (err) {
      console.error('Error loading more messages:', err);
      setError('Error loading more messages');
    }
  }, [selectedConversation, loadMessages]);

  // Función para vincular un cliente con una conversación
  const linkClientToConversation = async (conversationId: string, clientId: string) => {
    if (!user?.uid) return;

    try {
      const conversationRef = ref(rtdb, `conversations/${user.uid}/${conversationId}`);
      await update(conversationRef, {
        clientId: clientId,
        updatedAt: serverTimestamp()
      });

      // Actualizar el estado local
      setConversations(prev => prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, clientId }
          : conv
      ));

      if (selectedConversation?.id === conversationId) {
        setSelectedConversation(prev => prev ? { ...prev, clientId } : null);
      }
    } catch (err) {
      console.error('Error linking client to conversation:', err);
      throw err;
    }
  };

  // El sistema de notificaciones ahora se maneja en GlobalNotificationsContext
  // Este useEffect se ha removido para evitar duplicación

  // Actualizar el contador de mensajes no leídos
  useEffect(() => {
    const totalUnread = conversations.reduce((total, conv) => total + (conv.unreadCount || 0), 0);
    setUnreadMessagesCount(totalUnread);
  }, [conversations]);

  // Efecto para cargar conexiones de Meta y conversaciones
  useEffect(() => {
    let unsubscribeConversations: (() => void) | null = null;
    let isMounted = true; // Flag para evitar actualizar el estado si el componente se desmonta
    let isLoading = false; // Flag para evitar múltiples cargas simultáneas

    const loadData = async () => {
      if (isLoading || !isMounted) return;

      try {
        isLoading = true;
        setLoading(true);

        // Primero cargar las conexiones de Meta
        await loadMetaConnections();

        // Luego cargar las conversaciones
        if (isMounted) {
          unsubscribeConversations = await loadConversations();
        }
      } catch (error) {
        console.error('Error in loadData effect:', error);
        if (isMounted) {
          setLoading(false);
        }
      } finally {
        isLoading = false;
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Solo ejecutar si hay un usuario autenticado
    if (user?.uid) {
      // Usar setTimeout para evitar que se ejecute inmediatamente en cada renderizado
      const timeoutId = setTimeout(() => {
        loadData();
      }, 100);

      return () => {
        clearTimeout(timeoutId);
        isMounted = false;
        if (unsubscribeConversations) {
          unsubscribeConversations();
        }
      };
    } else {
      setLoading(false);
      return () => {
        isMounted = false;
      };
    }
  }, [user?.uid]);

  useEffect(() => {
    let isMounted = true; // Flag para evitar actualizar el estado si el componente se desmonta

    // Solo cargar mensajes si hay una conversación seleccionada con ID
    if (selectedConversation?.id) {
      loadMessages();
    } else {
      // Si no hay conversación seleccionada, limpiar mensajes y desactivar carga
      setMessages([]);
      setLoadingMessages(false);
    }

    return () => {
      isMounted = false;
      if (unsubscribeMessages.current) {
        unsubscribeMessages.current();
        unsubscribeMessages.current = null;
      }
    };
  }, [loadMessages, selectedConversation]);

  // Limpieza al desmontar el componente
  useEffect(() => {
    return () => {
      if (unsubscribeMessages.current) {
        unsubscribeMessages.current();
      }
    };
  }, []);

  // Alias para fetchConversations (para mantener compatibilidad con código existente)
  const fetchConversations = useCallback(async () => {
    return await refreshConversations();
  }, [refreshConversations]);

  const value = {
    conversations,
    selectedConversation,
    messages,
    loading,
    error,
    loadingMessages,
    selectConversation,
    sendMessage,
    loadMoreMessages,
    refreshConversations,
    fetchConversations,
    markAsRead,
    linkClientToConversation,
    unreadMessagesCount
  };

  return (
    <ConversationsContext.Provider value={value}>
      {children}
    </ConversationsContext.Provider>
  );
}

export const useConversations = () => {
  const context = useContext(ConversationsContext);
  if (context === undefined) {
    throw new Error('useConversations must be used within a ConversationsProvider');
  }
  return context;
};
