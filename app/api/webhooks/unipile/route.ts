import { NextRequest, NextResponse } from 'next/server';
import { db, database as rtdb } from '@/lib/firebase-admin';
import admin from 'firebase-admin';
import { v4 as uuidv4 } from 'uuid';
import { v2 as cloudinary } from 'cloudinary';
import { fetchAndSaveProfilePicture } from './profile-image';
import { fetchAndSaveInstagramProfilePicture } from './instagram-profile';
import { generateAssistantResponse } from '@/app/services/assistant';
import { sendAssistantResponse } from '@/app/services/assistant-sender';

// Configurar Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true
});

/**
 * Webhook para recibir notificaciones de Unipile sobre cuentas asociadas y mensajes
 * Este endpoint recibe:
 * 1. Actualizaciones cuando un usuario conecta o desconecta una cuenta
 * 2. Mensajes entrantes de las cuentas conectadas
 */
export async function POST(req: NextRequest) {
  try {
    console.log('🔔 UNIPILE WEBHOOK RECIBIDO 🔔');
    console.log('====================================================');

    // Registrar los headers recibidos
    console.log('📋 HEADERS RECIBIDOS:');
    const headersObj: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headersObj[key] = value;
    });
    console.log(JSON.stringify(headersObj, null, 2));

    // Verificar que el cuerpo de la solicitud sea JSON
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json(
        { error: 'Content-Type debe ser application/json' },
        { status: 400 }
      );
    }

    // Obtener los datos del webhook
    const webhookData = await req.json();
    console.log('📦 PAYLOAD COMPLETO DEL WEBHOOK:');
    console.log(JSON.stringify(webhookData, null, 2));
    console.log('====================================================');

    // Verificar si es un webhook de AccountStatus (redirigir al otro endpoint)
    if (webhookData.AccountStatus) {
      console.log('ℹ️ Webhook de AccountStatus recibido en endpoint incorrecto');
      console.log('ℹ️ Este tipo de webhook debe enviarse a /api/webhooks/unipile-accounts');
      return NextResponse.json({
        success: false,
        message: 'Este tipo de webhook debe enviarse a /api/webhooks/unipile-accounts'
      }, { status: 400 });
    }
    // Determinar el tipo de webhook basado en el evento
    else if (webhookData.event === 'message_received') {
      // Procesar mensaje entrante
      return await handleIncomingMessage(webhookData);
    } else {
      // Procesar notificación de conexión de cuenta (formato original)
      return await handleAccountConnection(webhookData);
    }
  } catch (error) {
    console.error('💥 ERROR PROCESANDO WEBHOOK DE UNIPILE:');
    console.error(error);
    console.log('====================================================');
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Procesa un mensaje entrante de Unipile
 * @param webhookData Datos del webhook con el mensaje entrante
 */
async function handleIncomingMessage(webhookData: any) {
  try {
    console.log('📨 PROCESANDO MENSAJE ENTRANTE DE UNIPILE');

    // Extraer datos relevantes del webhook
    const {
      account_id,
      account_type,
      chat_id,
      message,
      message_id,
      timestamp,
      attachments,
      sender,
      attendees = [],
      subject,
      provider_chat_id
    } = webhookData;

    if (!account_id || !chat_id || !message_id) {
      console.error('❌ Datos incompletos en el webhook de mensaje');
      return NextResponse.json(
        { error: 'Datos incompletos en el webhook' },
        { status: 400 }
      );
    }

    console.log(`📱 Cuenta: ${account_id} (${account_type})`);
    console.log(`💬 Chat ID: ${chat_id}`);
    console.log(`📝 Mensaje: ${message}`);
    console.log(`🕒 Timestamp: ${timestamp}`);
    console.log(`👤 Remitente: ${sender?.attendee_name || 'Desconocido'}`);

    // Verificar si es un grupo basado en el subject o en el número de participantes
    const isGroup = !!subject || (attendees && attendees.length > 2) || (provider_chat_id && provider_chat_id.includes('@g.us'));

    if (isGroup) {
      console.log(`👥 Detectado GRUPO: ${subject || 'Sin nombre'}`);
      console.log(`👥 Número de participantes: ${attendees.length}`);
    }

    // Obtener la clave API de Unipile desde las variables de entorno
    const apiKey = process.env.UNIPILE_API_KEY || process.env.NEXT_PUBLIC_UNIPILE_API_KEY;
    const apiUrl = process.env.UNIPILE_API_URL || process.env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api10.unipile.com:14039';

    if (!apiKey) {
      console.error('❌ No se encontró la clave API de Unipile en las variables de entorno');
      return NextResponse.json(
        { error: 'Configuración de API incompleta' },
        { status: 500 }
      );
    }

    // Verificar si el mensaje fue enviado por el tatuador
    // Para esto, necesitamos obtener la información de la cuenta del tatuador
    let isMessageFromBusiness = false;
    let businessProviderId = '';
    let businessPhoneNumber = '';
    
    // Verificar si el mensaje fue enviado por la cuenta del negocio
    // Para Instagram, usar el provider_messaging_id almacenado en la conexión
    if (account_type.toLowerCase() === 'instagram') {
      try {
        // Buscar la conexión para obtener el provider_messaging_id almacenado
        const connectionsRef = db.collection('unipileConnections');
        const connectionsQuery = await connectionsRef
          .where('accountId', '==', account_id)
          .where('active', '==', true)
          .get();
        
        if (!connectionsQuery.empty) {
          const connectionDoc = connectionsQuery.docs[0];
          const connectionData = connectionDoc.data();
          
          // Verificar si tenemos el provider_messaging_id almacenado
          if (connectionData.provider_messaging_id) {
            console.log(`🔍 Comparando provider_messaging_id almacenado: ${connectionData.provider_messaging_id}`);
            console.log(`🔍 Con el ID del remitente: ${sender?.attendee_provider_id}`);
            
            // Comparar con el ID del remitente
            if (sender?.attendee_provider_id === connectionData.provider_messaging_id) {
              isMessageFromBusiness = true;
              console.log(`✅ Mensaje detectado como enviado por el tatuador (provider_messaging_id coincidente)`);
            } else {
              console.log(`ℹ️ El mensaje NO es del tatuador (provider_messaging_id no coincide)`);
            }
          } else {
            console.warn(`⚠️ No se encontró provider_messaging_id almacenado, usando método alternativo`);
            
            // Método alternativo: comparar el nombre del remitente con el nombre de la cuenta
            if (sender?.attendee_name === connectionData.name) {
              isMessageFromBusiness = true;
              console.log(`✅ Mensaje detectado como enviado por el tatuador (nombre coincidente)`);
            } else {
              // Intentar obtener información de la cuenta desde la API de Unipile
              console.log(`🔍 Intentando verificar mediante API de Unipile`);
              
              const accountResponse = await fetch(
                `${apiUrl}/api/v1/accounts/${account_id}`,
                {
                  method: 'GET',
                  headers: {
                    'X-API-KEY': apiKey,
                    'accept': 'application/json'
                  }
                }
              );

              if (accountResponse.ok) {
                const accountData = await accountResponse.json();
                
                // Extraer el username de la cuenta conectada
                if (accountData.connection_params?.im?.username) {
                  const accountUsername = accountData.connection_params.im.username;
                  
                  // Comparar con el nombre del remitente
                  if (sender?.attendee_name === accountUsername) {
                    isMessageFromBusiness = true;
                    console.log(`✅ Mensaje detectado como enviado por el tatuador (username coincidente)`);
                    
                    // Actualizar la conexión con el provider_messaging_id para futuras comparaciones
                    if (sender?.attendee_provider_id) {
                      await connectionDoc.ref.update({
                        provider_messaging_id: sender.attendee_provider_id,
                        updatedAt: admin.firestore.FieldValue.serverTimestamp()
                      });
                      console.log(`✅ provider_messaging_id actualizado: ${sender.attendee_provider_id}`);
                    }
                  }
                }
              }
            }
          }
        } else {
          console.warn(`⚠️ No se encontró conexión activa para la cuenta ${account_id}`);
        }
      } catch (error) {
        console.error(`❌ Error al verificar si el mensaje es del tatuador:`, error);
      }
    }

    try {
      // Obtener información de la cuenta desde la API de Unipile
      console.log(`🔍 Verificando si el mensaje es del tatuador (account_id: ${account_id})`);

      const accountResponse = await fetch(
        `${apiUrl}/api/v1/accounts/${account_id}`,
        {
          method: 'GET',
          headers: {
            'X-API-KEY': apiKey,
            'accept': 'application/json'
          }
        }
      );

      if (accountResponse.ok) {
        const accountData = await accountResponse.json();
        console.log(`✅ Información de la cuenta obtenida:`, JSON.stringify(accountData, null, 2));

        // Extraer el ID o número de teléfono del tatuador desde connection_params
        if (accountData.connection_params && accountData.connection_params.im) {
          // Intentar obtener el ID primero
          if (accountData.connection_params.im.id) {
            businessProviderId = accountData.connection_params.im.id;
            console.log(`👤 ID del tatuador en la plataforma: ${businessProviderId}`);
          }

          // Intentar obtener el número de teléfono si está disponible
          if (accountData.connection_params.im.phone_number) {
            businessPhoneNumber = accountData.connection_params.im.phone_number;
            console.log(`📱 Número de teléfono del tatuador: ${businessPhoneNumber}`);
          }

          // Verificar si el mensaje es del tatuador usando varias estrategias
          if (sender && sender.attendee_provider_id) {
            // Estrategia 1: Comparación directa con el ID
            if (businessProviderId && sender.attendee_provider_id === businessProviderId) {
              isMessageFromBusiness = true;
              console.log(`✅ El mensaje fue enviado por el tatuador (coincidencia exacta de ID)`);
            }
            // Estrategia 2: Verificar si el número de teléfono está incluido en el ID del remitente
            else if (businessPhoneNumber && sender.attendee_provider_id.includes(businessPhoneNumber)) {
              isMessageFromBusiness = true;
              console.log(`✅ El mensaje fue enviado por el tatuador (número de teléfono incluido en ID)`);
            }
            // Estrategia 3: Para WhatsApp, comparar el número sin el sufijo @s.whatsapp.net
            else if (businessPhoneNumber && sender.attendee_provider_id.split('@')[0] === businessPhoneNumber) {
              isMessageFromBusiness = true;
              console.log(`✅ El mensaje fue enviado por el tatuador (coincidencia de número sin sufijo)`);
            }
            // No es un mensaje del tatuador
            else {
              console.log(`ℹ️ El mensaje fue enviado por el cliente`);
              console.log(`- ID del remitente: ${sender.attendee_provider_id}`);
              console.log(`- ID del tatuador: ${businessProviderId || 'No disponible'}`);
              console.log(`- Número del tatuador: ${businessPhoneNumber || 'No disponible'}`);
            }
          }
        } else {
          console.warn(`⚠️ No se pudo obtener información del tatuador desde connection_params`);
        }
      } else {
        console.error(`❌ Error al obtener información de la cuenta: ${accountResponse.statusText}`);
      }
    } catch (error) {
      console.error(`❌ Error al verificar si el mensaje es del tatuador:`, error);
    }

    // Buscar a qué usuario pertenece esta cuenta
    const connectionsRef = db.collection('unipileConnections');
    const connectionsQuery = await connectionsRef
      .where('accountId', '==', account_id)
      .where('active', '==', true)
      .get();

    if (connectionsQuery.empty) {
      console.error(`❌ No se encontró ningún usuario con la cuenta ${account_id} activa`);
      return NextResponse.json(
        { error: 'Cuenta no asociada a ningún usuario' },
        { status: 404 }
      );
    }

    // Obtener el documento de conexión
    const connectionDoc = connectionsQuery.docs[0];
    const connectionData = connectionDoc.data();
    const userId = connectionData.userId;

    console.log(`👤 Usuario asociado: ${userId}`);

    // Generar un ID único para el mensaje
    const messageId = `${message_id}`;

    // Usar la instancia de RTDB importada correctamente
    const conversationId = chat_id;

    // Verificar si la conversación ya existe
    const conversationRef = rtdb.ref(`conversations/${userId}/${conversationId}`);
    const conversationSnapshot = await conversationRef.once('value');
    const conversationExists = conversationSnapshot.exists();

    // Información del remitente
    const senderName = sender?.attendee_name || 'Usuario';
    const senderPhoneOrId = sender?.attendee_provider_id?.split('@')[0] || '';

    // Determinar el nombre de la conversación
    // Para grupos, usar el subject (nombre del grupo)
    // Para conversaciones individuales, usar el nombre del remitente
    const conversationName = isGroup
      ? (subject || `Grupo (${attendees.length} participantes)`)
      : senderName;

    // Crear o actualizar la conversación
    if (!conversationExists) {
      // Crear nueva conversación
      const newConversationData: any = {
        id: conversationId,
        participantName: conversationName,
        participantId: sender?.attendee_id || '',
        participantPhone: senderPhoneOrId,
        platform: account_type.toLowerCase(),
        lastMessage: {
          content: message || 'Mensaje multimedia',
          timestamp: Date.now()
        },
        lastMessageAt: Date.now(),
        createdAt: Date.now(),
        updatedAt: Date.now(),
        unread: !isMessageFromBusiness, // Solo marcar como no leído si el mensaje es del cliente
        accountId: account_id,
        attendeeId: sender?.attendee_id || '', // Guardar el attendeeId para la imagen de perfil
        provider: 'sinch', // Indicar que es una conversación de Sinch/Unipile
        unipileChatId: chat_id, // Guardar el chat_id de Unipile para usarlo en el envío de mensajes
        externalId: chat_id, // Guardar también como externalId para compatibilidad
        chat_id: chat_id, // Guardar explícitamente como chat_id para mayor claridad
        businessProviderId: businessProviderId, // Guardar el ID del tatuador para futuras comparaciones
        businessPhoneNumber: businessPhoneNumber // Guardar el número de teléfono del tatuador para futuras comparaciones
      };

      // Añadir campos específicos para grupos
      if (isGroup) {
        newConversationData.isGroup = true;
        newConversationData.groupName = subject;
        newConversationData.participantCount = attendees.length;
        // Guardar los IDs de los participantes para referencia
        newConversationData.groupParticipants = attendees.map(a => ({
          id: a.attendee_id,
          name: a.attendee_name,
          providerId: a.attendee_provider_id
        }));
      }

      await conversationRef.set(newConversationData);

      console.log(`✅ Conversación ${isGroup ? 'de grupo' : ''} creada: ${conversationId}`);
    } else {
      // Actualizar conversación existente
      const updateData: any = {
        lastMessage: {
          content: message || 'Mensaje multimedia',
          timestamp: Date.now()
        },
        lastMessageAt: Date.now(),
        updatedAt: Date.now()
      };

      // Solo marcar como no leído si el mensaje es del cliente, no del tatuador
      if (!isMessageFromBusiness) {
        updateData.unread = true;
      }

      // Añadir el attendeeId si está disponible y no existe ya
      if (sender?.attendee_id && !conversationSnapshot.val().attendeeId) {
        updateData.attendeeId = sender.attendee_id;
        updateData.provider = 'sinch'; // Indicar que es una conversación de Sinch/Unipile
      }

      // Asegurarse de que el chat_id de Unipile esté guardado en todos los campos necesarios
      if (!conversationSnapshot.val().unipileChatId || !conversationSnapshot.val().chat_id) {
        updateData.unipileChatId = chat_id;
        updateData.externalId = chat_id; // También guardar como externalId para compatibilidad
        updateData.chat_id = chat_id; // Guardar explícitamente como chat_id para mayor claridad
        console.log(`✅ Actualizando IDs de chat: ${chat_id}`);
      }

      // Actualizar los IDs del negocio si han cambiado o no existen
      if (businessProviderId && (!conversationSnapshot.val().businessProviderId || conversationSnapshot.val().businessProviderId !== businessProviderId)) {
        updateData.businessProviderId = businessProviderId;
        console.log(`✅ Actualizando ID del tatuador: ${businessProviderId}`);
      }

      // Actualizar el número de teléfono del negocio si ha cambiado o no existe
      if (businessPhoneNumber && (!conversationSnapshot.val().businessPhoneNumber || conversationSnapshot.val().businessPhoneNumber !== businessPhoneNumber)) {
        updateData.businessPhoneNumber = businessPhoneNumber;
        console.log(`✅ Actualizando número de teléfono del tatuador: ${businessPhoneNumber}`);
      }

      // Actualizar el nombre de la conversación si es un grupo y ha cambiado
      const existingData = conversationSnapshot.val();
      if (isGroup) {
        // Si es un grupo y no estaba marcado como tal, o si el nombre ha cambiado
        if (!existingData.isGroup || (subject && existingData.groupName !== subject)) {
          updateData.isGroup = true;
          updateData.groupName = subject;
          updateData.participantName = conversationName;

          // Actualizar la lista de participantes si ha cambiado
          if (!existingData.groupParticipants ||
              existingData.participantCount !== attendees.length) {
            updateData.participantCount = attendees.length;
            updateData.groupParticipants = attendees.map(a => ({
              id: a.attendee_id,
              name: a.attendee_name,
              providerId: a.attendee_provider_id
            }));
          }

          console.log(`👥 Actualizando información de grupo: ${subject || 'Sin nombre'}`);
        }
      }

      await conversationRef.update(updateData);

      console.log(`✅ Conversación ${isGroup ? 'de grupo' : ''} actualizada: ${conversationId}`);
    }    // Procesar archivos adjuntos si existen
    let processedAttachments: string[] = [];

    if (attachments && attachments.length > 0) {
      console.log(`📎 Procesando ${attachments.length} archivos adjuntos`);

      for (const attachment of attachments) {
        try {
          const { attachment_id, attachment_type } = attachment;

          if (!attachment_id) {
            console.warn('⚠️ Adjunto sin ID, omitiendo');
            continue;
          }

          // Obtener el archivo adjunto de la API de Unipile
          console.log(`📥 Descargando adjunto ${attachment_id}`);

          const attachmentResponse = await fetch(
            `${apiUrl}/api/v1/messages/${message_id}/attachments/${attachment_id}`,
            {
              method: 'GET',
              headers: {
                'X-API-KEY': apiKey,
                'accept': 'application/json'
              }
            }
          );

          if (!attachmentResponse.ok) {
            console.error(`❌ Error al descargar adjunto: ${attachmentResponse.statusText}`);
            continue;
          }

          // Obtener los datos binarios
          const attachmentBuffer = Buffer.from(await attachmentResponse.arrayBuffer());

          // Detectar el tipo de archivo basado en los magic bytes
          let contentType = 'application/octet-stream'; // Valor predeterminado
          let fileExtension = '';

          // Detectar tipo basado en magic bytes (bytes iniciales del archivo)
          const magicBytes = attachmentBuffer.subarray(0, 16);
          const magicHex = magicBytes.toString('hex').toLowerCase();

          console.log(`🔍 Magic bytes: ${magicHex}`);

          // JPEG: comienza con FFD8
          if (magicHex.startsWith('ffd8')) {
            contentType = 'image/jpeg';
            fileExtension = '.jpg';
          }
          // PNG: comienza con 89504E47 (‰PNG)
          else if (magicHex.startsWith('89504e47')) {
            contentType = 'image/png';
            fileExtension = '.png';
          }
          // GIF: comienza con 474946 (GIF)
          else if (magicHex.startsWith('474946')) {
            contentType = 'image/gif';
            fileExtension = '.gif';
          }
          // PDF: comienza con 25504446 (%PDF)
          else if (magicHex.startsWith('25504446')) {
            contentType = 'application/pdf';
            fileExtension = '.pdf';
          }
          // MP3: comienza con 494433 (ID3) o FFFB (MPEG frame sync)
          else if (magicHex.startsWith('494433') || magicHex.includes('fffb')) {
            contentType = 'audio/mpeg';
            fileExtension = '.mp3';
          }
          // OPUS: comienza con 4F70757348656164 (OpusHead)
          else if (magicHex.includes('4f70757348656164') || magicHex.includes('opus')) {
            contentType = 'audio/opus';
            fileExtension = '.opus';
          }
          // OGG: comienza con 4F676753 (OggS)
          else if (magicHex.startsWith('4f676753')) {
            contentType = 'audio/ogg';
            fileExtension = '.ogg';
          }
          // MP4/MPEG4: comienza con 00000018 o varios otros patrones
          else if (magicHex.includes('6674797069736f6d') || magicHex.includes('ftyp')) {
            contentType = 'video/mp4';
            fileExtension = '.mp4';
          }
          // WEBP: contiene WEBP en los primeros bytes
          else if (magicHex.includes('57454250')) {
            contentType = 'image/webp';
            fileExtension = '.webp';
          }
          // Fallback a attachment_type si no pudimos detectar por magic bytes
          else {
            if (attachment_type === 'img') {
              contentType = 'image/jpeg';
              fileExtension = '.jpg';
            } else if (attachment_type === 'video') {
              contentType = 'video/mp4';
              fileExtension = '.mp4';
            } else if (attachment_type === 'audio') {
              contentType = 'audio/mpeg';
              fileExtension = '.mp3';
            } else if (attachment_type === 'doc') {
              contentType = 'application/pdf';
              fileExtension = '.pdf';
            }
          }

          console.log(`🧩 Tipo de contenido detectado: ${contentType} (${fileExtension})`);

          // Generar un nombre de archivo único con la extensión correcta
          const uniqueFilename = `${uuidv4()}${fileExtension}`;

          // Convertir a base64 para subir a Cloudinary
          const base64Data = `data:${contentType};base64,${attachmentBuffer.toString('base64')}`;

          // Subir a Cloudinary
          try {
            const uploadResult = await new Promise((resolve, reject) => {
              cloudinary.uploader.upload(
                base64Data,
                {
                  folder: `unipile/${userId}/${conversationId}`,
                  public_id: uniqueFilename.split('.')[0], // Usar el nombre único pero sin la extensión
                  resource_type: 'auto',
                  format: fileExtension.replace('.', ''), // Especificar el formato sin el punto
                  transformation: contentType.startsWith('image/') ? [
                    { quality: 'auto', fetch_format: 'auto' },
                    { width: 800, crop: 'limit' }
                  ] : []
                },
                (error, result) => {
                  if (error) reject(error);
                  else resolve(result);
                }
              );
            });

            // Extraer la URL segura del resultado
            const mediaUrl = (uploadResult as any).secure_url;

            // Agregar la URL a la lista de adjuntos procesados
            processedAttachments.push(mediaUrl);

            console.log(`✅ Adjunto subido a Cloudinary: ${mediaUrl}`);
          } catch (cloudinaryError) {
            console.error('❌ Error al subir a Cloudinary:', cloudinaryError);
            throw cloudinaryError;
          }
        } catch (attachmentError) {
          console.error('❌ Error procesando adjunto:', attachmentError);
        }
      }
    }


    // Guardar el mensaje en Realtime Database
    const messageRef = rtdb.ref(`messages/${conversationId}/${messageId}`);

    // Definir la interfaz para el mensaje con todos los campos posibles
    interface MessageData {
      id: string;
      content: string;
      timestamp: number;
      sender: string;
      read: boolean;
      platform: string;
      senderName: string;
      senderPhone: string;
      attachments?: string[];
      // Campos adicionales para mensajes de grupo
      isGroupMessage?: boolean;
      groupName?: string;
      groupSenderName?: string;
    }

    const messageData: MessageData = {
      id: messageId,
      content: message || '',
      timestamp: new Date(timestamp).getTime() || Date.now(),
      sender: isMessageFromBusiness ? 'business' : 'user', // Marcar como 'business' si es del tatuador
      read: isMessageFromBusiness, // Marcar como leído si es del tatuador
      platform: account_type.toLowerCase(),
      senderName: senderName,
      senderPhone: senderPhoneOrId
    };

    // Agregar información de grupo si es un mensaje de grupo
    if (isGroup) {
      messageData.isGroupMessage = true;
      messageData.groupName = subject;
      // Añadir el nombre del remitente para mostrar quién envió el mensaje en el grupo
      messageData.groupSenderName = senderName;
    }

    // Agregar adjuntos si existen
    if (processedAttachments.length > 0) {
      messageData.attachments = processedAttachments;
    }

    await messageRef.set(messageData);

    if (isMessageFromBusiness) {
      console.log(`✅ Mensaje del TATUADOR guardado en Realtime Database: ${messageId}`);
    } else {
      console.log(`✅ Mensaje del CLIENTE guardado en Realtime Database: ${messageId}`);
    }

    // Guardar mensaje en RTDB con más detalles para tener un registro completo
    const detailedMessageRef = rtdb.ref(`conversations/${userId}/${conversationId}/messages/${message_id}`);
    const detailedMessageData: any = {
      id: message_id,
      content: message || '',
      attachments: processedAttachments,
      sender: sender ? {
        id: sender.attendee_id,
        providerId: sender.attendee_provider_id,
        name: sender.attendee_name
      } : {},
      timestamp: Date.now(),
      type: webhookData.message_type || 'text',
      isFromBusiness: isMessageFromBusiness, // Indicar si el mensaje es del tatuador
      senderType: isMessageFromBusiness ? 'business' : 'user', // Tipo de remitente
      externalId: message_id, // ID externo del mensaje para referencia
      externalTimestamp: new Date(timestamp).getTime() // Timestamp externo para referencia
    };

    // Añadir información de grupo si es un mensaje de grupo
    if (isGroup) {
      detailedMessageData.isGroupMessage = true;
      detailedMessageData.groupName = subject;
      detailedMessageData.fromGroup = true;
      // Incluir información sobre el grupo para referencia
      detailedMessageData.group = {
        name: subject,
        participantCount: attendees.length,
        chatId: chat_id,
        providerId: provider_chat_id
      };
    }

    await detailedMessageRef.set(detailedMessageData);

    // Obtener y guardar la imagen de perfil
    // Para grupos, usamos el chat_id para obtener la imagen del grupo
    // Para conversaciones individuales, usamos el attendee_id del remitente
    // SOLO si el mensaje NO es del tatuador
    try {
      // Solo procesar la imagen de perfil si es un grupo o si el mensaje es del cliente (no del tatuador)
      if (isGroup || !isMessageFromBusiness) {
        // Determinar qué ID usar para obtener la imagen de perfil
        const profileImageId = isGroup ? chat_id : (sender?.attendee_id || '');
        const profileImageType = isGroup ? 'chat' : 'attendee';

        if (profileImageId) {
          console.log(`🖼️ Intentando obtener imagen de perfil para ${profileImageType}: ${profileImageId}`);
          if (isGroup) {
            console.log(`👥 Obteniendo imagen de perfil para GRUPO: ${subject || 'Sin nombre'}`);
          } else {
            console.log(`👤 Obteniendo imagen de perfil para REMITENTE: ${sender?.attendee_name || 'Desconocido'}`);
          }

          // Verificar si la conversación ya tiene una imagen de perfil
          let skipProfilePicture = false;
          let lastProfileAttempt = 0;
          let existingProfileUrl = '';
          
          try {
            const conversationRef = rtdb.ref(`conversations/${userId}/${conversationId}`);
            const conversationSnapshot = await conversationRef.get();

            if (conversationSnapshot.exists()) {
              const conversationData = conversationSnapshot.val();
              
              // Verificar si ya hay una imagen de perfil
              if (conversationData.profilePictureUrl) {
                existingProfileUrl = conversationData.profilePictureUrl;
                console.log(`ℹ️ La conversación ya tiene una imagen de perfil: ${existingProfileUrl}`);
                
                // Verificar cuándo fue el último intento de obtener la imagen
                lastProfileAttempt = conversationData.profilePictureUpdatedAt || 0;
                const now = Date.now();
                const daysSinceLastAttempt = (now - lastProfileAttempt) / (1000 * 60 * 60 * 24);
                
                // Si la imagen existe y se obtuvo hace menos de 7 días, no volver a intentar
                if (daysSinceLastAttempt < 7) {
                  console.log(`ℹ️ La imagen de perfil se obtuvo hace ${daysSinceLastAttempt.toFixed(1)} días, no se volverá a intentar`);
                  skipProfilePicture = true;
                } else {
                  console.log(`ℹ️ La imagen de perfil se obtuvo hace ${daysSinceLastAttempt.toFixed(1)} días, se intentará actualizar`);
                }
              } else if (conversationData.profilePictureAttempts) {
                // Si no hay imagen pero ya se intentó obtenerla anteriormente
                const attempts = conversationData.profilePictureAttempts || 0;
                lastProfileAttempt = conversationData.lastProfileAttempt || 0;
                const now = Date.now();
                const hoursSinceLastAttempt = (now - lastProfileAttempt) / (1000 * 60 * 60);
                
                // Durante desarrollo, permitir siempre obtener la imagen
                console.log(`📷 Intentando obtener imagen de perfil (intentos previos: ${attempts}, último intento hace ${hoursSinceLastAttempt.toFixed(2)} horas)`);
                
                // Reiniciar el contador de intentos si han pasado más de 1 hora
                if (hoursSinceLastAttempt > 1) {
                  console.log(`🔄 Reiniciando contador de intentos por tiempo transcurrido`);
                  await conversationRef.update({
                    profilePictureAttempts: 0
                  });
                }
              }
            }
          } catch (checkError) {
            console.error('❌ Error al verificar imagen de perfil existente:', checkError);
          }

          // Solo continuar si no hay que omitir la obtención de la imagen
          if (!skipProfilePicture) {
            // Buscar si hay un cliente asociado a esta conversación
            let associatedClientId: string | undefined = undefined;

            try {
              // Buscar en Firestore si hay un cliente asociado a esta conversación
              const clientsRef = db.collection('clients');
              const clientsQuery = await clientsRef
                .where('artistId', '==', userId)
                .where('conversationId', '==', conversationId)
                .limit(1)
                .get();

              if (!clientsQuery.empty) {
                associatedClientId = clientsQuery.docs[0].id;
                console.log(`✅ Cliente encontrado para esta conversación: ${associatedClientId}`);
              } else {
                console.log(`ℹ️ No se encontró un cliente asociado a esta conversación`);
              }
            } catch (clientError) {
              console.error('❌ Error al buscar cliente asociado:', clientError);
              // Continuamos sin clientId si hay error
            }

            // Usar el chat_id como conversationId para la imagen de perfil
            const profileConversationId = chat_id || conversationId;

            // Determinar qué ID y provider ID usar según si es grupo o individual
            const idForProfilePicture = isGroup ? chat_id : sender.attendee_id;
            const providerIdForProfilePicture = isGroup
              ? `group_${chat_id}`
              : (sender.attendee_provider_id || `${sender.attendee_id}_unknown`);

            // Para Instagram, usar la nueva función específica
            // O intentar extraer un nombre de usuario de Instagram de cualquier plataforma si no hay foto
            if (account_type.toLowerCase() === 'instagram' || !existingProfileUrl) {
              // Determinar nombre de usuario para obtener la imagen
              let username = '';
              // Variable para almacenar el attendee_id que usaremos
              let selectedAttendeeId = '';
              
              // Si no es Instagram pero estamos intentando como fallback, registrarlo
              if (account_type.toLowerCase() !== 'instagram') {
                console.log(`🔄 No hay foto de perfil existente, intentando extraer nombre de usuario de Instagram como fallback`);
              }
              
              // Determinar si es un grupo real o un chat individual
              // Un chat individual en Instagram puede tener subject igual al nombre del usuario
              // y solo un attendee, pero no es realmente un grupo
              const isRealGroup = isGroup && (attendees.length > 1 || (subject && subject !== attendees[0]?.attendee_name));
              
              if (isRealGroup) {
                // Para grupos, usar el subject como nombre de usuario o el primer attendee
                console.log(`📸 Detectado GRUPO de Instagram: ${subject}`);
                
                if (subject && subject !== 'null' && subject !== 'undefined') {
                  username = subject;
                } else if (attendees && attendees.length > 0 && attendees[0].attendee_name) {
                  username = attendees[0].attendee_name;
                  selectedAttendeeId = attendees[0].attendee_id; // Guardar el attendee_id del primer asistente
                }
                
                // Verificar si hay URL de perfil en algún attendee
                for (const attendee of attendees) {
                  if (attendee.attendee_profile_url && attendee.attendee_profile_url.includes('instagram.com/')) {
                    const urlParts = attendee.attendee_profile_url.split('instagram.com/');
                    if (urlParts.length > 1) {
                      username = urlParts[1].replace(/\/$/, '').split('?')[0].split('/')[0];
                      selectedAttendeeId = attendee.attendee_id; // Guardar el attendee_id del asistente con URL de Instagram
                      break;
                    }
                  }
                }
                
                // Si no encontramos un attendee_id específico pero hay asistentes, usar el primero
                if (!selectedAttendeeId && attendees && attendees.length > 0 && attendees[0].attendee_id) {
                  selectedAttendeeId = attendees[0].attendee_id;
                  console.log(`📸 Usando attendee_id del primer asistente: ${selectedAttendeeId}`);
                }
              } else if (sender?.attendee_name) {
                // Para chats individuales, usar el nombre del remitente
                console.log(`📸 Usando método específico para Instagram con usuario: ${sender.attendee_name}`);
                username = sender.attendee_name;
                
                // Obtener URL de perfil si está disponible
                const profileUrl = sender?.attendee_profile_url || '';
                
                // Extraer el nombre de usuario de la URL
                if (profileUrl && profileUrl.includes('instagram.com/')) {
                  const urlParts = profileUrl.split('instagram.com/');
                  if (urlParts.length > 1) {
                    // Eliminar cualquier barra final y parámetros de consulta
                    username = urlParts[1].replace(/\/$/, '').split('?')[0].split('/')[0];
                  }
                }
              }
              
              // Solo proceder si tenemos un nombre de usuario
              if (username) {
                // Si no es Instagram pero estamos usando como fallback, registrarlo
                if (account_type.toLowerCase() !== 'instagram') {
                  console.log(`🔍 Usando Apify como fallback para obtener foto de perfil de: ${username}`);
                }
                console.log(`📸 Obteniendo foto de perfil de Instagram para: ${username}`);
                
                // Determinar qué ID usar para obtener la foto de perfil
                // REGLA IMPORTANTE: Para grupos REALES usar chat_id, para individuales usar attendee_id
                const idToUse = isRealGroup
                  ? chat_id  // Para grupos reales, usar el chat_id
                  : sender?.attendee_id; // Para chats individuales, usar el attendee_id del remitente
                
                // Registrar el ID que se va a usar
                if (idToUse) {
                  console.log(`📸 Usando ${isRealGroup ? 'chat_id' : 'attendee_id'} para obtener foto: ${idToUse}`);
                  // Añadir información adicional para depuración
                  console.log(`📸 Tipo de chat: ${isRealGroup ? 'GRUPO REAL' : 'INDIVIDUAL'} (attendees: ${attendees.length}, subject: ${subject})`);
                } else {
                  console.log(`⚠️ No se pudo determinar un ID para obtener la foto de perfil`);
                }
                
                // Llamar a la función específica para Instagram
                fetchAndSaveInstagramProfilePicture(
                  username,
                  profileConversationId,
                  undefined, // groupName
                  idToUse // Pasar el ID correcto según si es grupo (chat_id) o individual (attendee_id)
                ).catch(error => {
                  console.error('❌ Error al obtener/guardar imagen de perfil de Instagram:', error);
                });
              } else {
                console.log('⚠️ No se pudo determinar un nombre de usuario de Instagram para obtener la foto de perfil');
                
                // Si no es Instagram y no tenemos foto, intentar buscar cualquier mención de Instagram en el mensaje
                if (account_type.toLowerCase() !== 'instagram' && !existingProfileUrl && message) {
                  // Buscar menciones de Instagram en el mensaje
                  const instagramRegex = /@([A-Za-z0-9_\.]+)/g;
                  const instagramMatches = message.match(instagramRegex);
                  
                  if (instagramMatches && instagramMatches.length > 0) {
                    // Usar la primera mención como nombre de usuario
                    const possibleUsername = instagramMatches[0].replace('@', '');
                    console.log(`🔍 Se encontró posible usuario de Instagram en el mensaje: ${possibleUsername}`);
                    
                    // Intentar obtener la foto con este usuario
                    fetchAndSaveInstagramProfilePicture(
                      possibleUsername,
                      profileConversationId,
                      undefined, // groupName
                      sender?.attendee_id // Pasar el attendee_id para obtener la foto directamente
                    ).catch(error => {
                      console.error(`❌ Error al obtener foto de perfil para posible usuario ${possibleUsername}:`, error);
                    });
                  } else {
                    console.log('⚠️ No se encontraron menciones de Instagram en el mensaje');
                  }
                }
              }
            } else {
              // Para otras plataformas, usar la función original
              fetchAndSaveProfilePicture(
                idForProfilePicture,
                providerIdForProfilePicture,
                userId,
                profileConversationId,
                associatedClientId,
                isGroup
              ).catch(error => {
                console.error('❌ Error al obtener imagen de perfil:', error);
              });
            }
          }
        }
      } else {
        console.log(`ℹ️ No se obtiene imagen de perfil porque el mensaje es del tatuador`);
      }
    } catch (error) {
      console.error('❌ Error al iniciar obtención de imagen de perfil:', error);
      // No interrumpimos el flujo principal si falla la obtención de la imagen
    }

    // Procesar con el asistente de IA si el mensaje es del cliente (no del tatuador)
    if (!isMessageFromBusiness) {
      try {
        console.log('🤖 Verificando si se debe procesar con asistente de IA...');

        // Obtener la configuración del asistente para el usuario
        const userAssistantConfigRef = rtdb.ref(`settings/users/${userId}/assistant`);
        const userAssistantConfigSnapshot = await userAssistantConfigRef.get();

        if (userAssistantConfigSnapshot.exists()) {
          const userAssistantConfig = userAssistantConfigSnapshot.val();

          // Verificar si el asistente está habilitado globalmente
          if (userAssistantConfig.isEnabled) {
            console.log('🤖 Asistente habilitado globalmente para usuario:', userId);

            // Verificar si la IA está activada para esta conversación específica
            const aiStateRef = rtdb.ref(`conversations/${userId}/${conversationId}/aiEnabled`);
            const aiStateSnapshot = await aiStateRef.get();

            // Si aiEnabled está definido como false, no procesar con IA
            if (aiStateSnapshot.exists() && aiStateSnapshot.val() === false) {
              console.log(`🚫 IA desactivada específicamente para la conversación ${conversationId}, omitiendo procesamiento`);
            } else {
              // Obtener mensajes previos para contexto
              const messagesRef = rtdb.ref(`messages/${conversationId}`);
              const messagesSnapshot = await messagesRef.orderByChild('timestamp').limitToLast(10).get();

              const previousMessages = [];
              if (messagesSnapshot.exists()) {
                messagesSnapshot.forEach((childSnapshot) => {
                  const msg = childSnapshot.val();
                  previousMessages.push({
                    content: msg.content || '',
                    timestamp: msg.timestamp,
                    sender: msg.sender || (msg.isFromBusiness ? 'business' : 'user')
                  });
                });
              }

              // Generar el prompt completo a partir de basePrompt y requiredFields
              let systemPrompt = '';
              if (userAssistantConfig.basePrompt) {
                // Nuevo formato con basePrompt y requiredFields separados
                const requiredFieldsText = userAssistantConfig.requiredFields
                  .map((field, index) => `${index + 1}. ${field}`)
                  .join('\n');

                systemPrompt = `${userAssistantConfig.basePrompt}

DATOS MÍNIMOS A RECOPILAR:
Es OBLIGATORIO que obtengas los siguientes datos del cliente interesado en tatuarse:
${requiredFieldsText}`;
              } else if (userAssistantConfig.systemPrompt) {
                // Formato antiguo con systemPrompt
                systemPrompt = userAssistantConfig.systemPrompt;
              } else {
                // Prompt por defecto
                systemPrompt = "Eres un asistente amable y profesional para un estudio de tatuajes.";
              }

              // Obtener nombre del cliente
              let clientName = sender?.attendee_name || 'Cliente';

              // Generar respuesta del asistente
              try {
                console.log('🤖 Generando respuesta del asistente...');

                const assistantResponse = await generateAssistantResponse(
                  message || 'Hola',
                  systemPrompt,
                  previousMessages,
                  conversationId,
                  sender?.attendee_id,
                  clientName,
                  userAssistantConfig.requiredFields,
                  account_type || 'Chat',
                  chat_id
                );

                // Si hay una respuesta, enviarla
                if (assistantResponse) {
                  console.log('💬 Respuesta del asistente generada:', assistantResponse);

                  // Enviar respuesta a través de Unipile
                  await sendAssistantResponse(userId, conversationId, assistantResponse);
                } else {
                  console.log('⚠️ No hay respuesta del asistente para enviar');
                }
              } catch (aiError) {
                console.error('❌ Error generando respuesta del asistente:', aiError);
              }
            }
          } else {
            console.log('🤖 Asistente deshabilitado para usuario:', userId);
          }
        } else {
          console.log('⚠️ No se encontró configuración del asistente para el usuario:', userId);
        }
      } catch (assistantError) {
        console.error('❌ Error procesando asistente de IA:', assistantError);
        // No interrumpimos el flujo principal si falla el asistente
      }
    }

    console.log('✅ MENSAJE PROCESADO CORRECTAMENTE');
    console.log('====================================================');

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('❌ ERROR PROCESANDO MENSAJE ENTRANTE:');
    console.error(error);
    console.log('====================================================');
    return NextResponse.json(
      {
        error: 'Error procesando mensaje entrante',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
/**
 * Procesa una notificación de conexión/desconexión de cuenta
 * @param webhookData Datos del webhook con la información de la cuenta
 */
async function handleAccountConnection(webhookData: any) {
  try {
    // Extraer los datos del nuevo formato de webhook
    const { status, account_id, name } = webhookData;

    // Verificar que tenemos los datos necesarios
    if (!account_id) {
      console.error('❌ No se recibió un ID de cuenta válido');
      return NextResponse.json(
        { error: 'No se recibió un ID de cuenta válido' },
        { status: 400 }
      );
    }

    if (!name) {
      console.error('❌ No se recibió el ID del usuario (name)');
      return NextResponse.json(
        { error: 'No se recibió el ID del usuario' },
        { status: 400 }
      );
    }

    // Usar el name como userId (ID del tatuador)
    const userId = name;

    console.log(`🔑 ID de cuenta recibido: ${account_id}`);
    console.log(`👤 ID de usuario (tatuador): ${userId}`);
    console.log(`💬 Estado: ${status}`);
    console.log('====================================================');

    // Obtener la clave API de Unipile desde las variables de entorno
    const apiKey = process.env.UNIPILE_API_KEY || process.env.NEXT_PUBLIC_UNIPILE_API_KEY;
    const apiUrl = process.env.UNIPILE_API_URL || process.env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api10.unipile.com:14039';

    if (!apiKey) {
      console.error('❌ No se encontró la clave API de Unipile en las variables de entorno');
      return NextResponse.json(
        { error: 'Configuración de API incompleta' },
        { status: 500 }
      );
    }

    // Realizar una llamada a la API de Unipile para obtener los detalles de la cuenta
    console.log(`💬 Consultando detalles de la cuenta ${account_id} en Unipile...`);

    let platform = 'unknown';
    try {
      const accountResponse = await fetch(`${apiUrl}/api/v1/accounts/${account_id}`, {
        method: 'GET',
        headers: {
          'X-API-KEY': apiKey,
          'accept': 'application/json'
        }
      });

      if (!accountResponse.ok) {
        throw new Error(`Error al consultar la cuenta: ${accountResponse.statusText}`);
      }

      const accountData = await accountResponse.json();
      console.log('💬 DETALLES DE LA CUENTA RECIBIDOS:');
      console.log(JSON.stringify(accountData, null, 2));

      // Extraer el tipo de cuenta de los detalles
      if (accountData.type) {
        // Convertir a minúsculas para consistencia
        platform = accountData.type.toLowerCase();
        console.log(`📱 Tipo de cuenta detectado: ${accountData.type} -> ${platform}`);
      } else {
        console.log('⚠️ No se pudo determinar el tipo de cuenta desde la respuesta de la API. Se usará "unknown".');
      }
    } catch (apiError) {
      console.error('❌ Error al consultar detalles de la cuenta:', apiError);
      console.log('⚠️ Continuando con el procesamiento usando la información disponible...');
    }

    // Determinar si la cuenta está siendo conectada o desconectada
    const isConnected = status === 'CREATION_SUCCESS';

    // Actualizar la información en Firestore
    try {
      // Referencia al documento del usuario
      const userRef = db.collection('users').doc(userId);

      // Comprobar si el usuario existe
      const userDoc = await userRef.get();
      if (!userDoc.exists) {
        console.error(`❌ Usuario ${userId} no encontrado en Firestore`);
        return NextResponse.json(
          { error: 'Usuario no encontrado' },
          { status: 404 }
        );
      }

      // Actualizar el estado de la cuenta en Firestore
      // Usar la plataforma determinada anteriormente
      const accountTypeKey = platform.toLowerCase();

      // Crear o actualizar el documento de conexión en la colección unipileConnections
      const connectionRef = db.collection('unipileConnections').doc(`${userId}_${accountTypeKey}`);

      if (isConnected) {
        // Si la cuenta está siendo conectada
        await connectionRef.set({
          userId,
          accountId: account_id,
          platform: accountTypeKey,
          connected: true,
          active: true, // Agregar campo 'active' para compatibilidad con el frontend
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        }, { merge: true });

        // Actualizar el estado en el documento del usuario
        await userRef.update({
          [`unipileAccounts.${accountTypeKey}`]: true,
          unipileConfigured: true,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`✅ CONEXIÓN EXITOSA: Cuenta ${accountTypeKey} conectada para el usuario ${userId}`);
      } else {
        // Si la cuenta está siendo desconectada
        await connectionRef.update({
          connected: false,
          active: false, // Actualizar campo 'active' para compatibilidad con el frontend
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Actualizar el estado en el documento del usuario
        await userRef.update({
          [`unipileAccounts.${accountTypeKey}`]: false,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`❌ DESCONEXIÓN: Cuenta ${accountTypeKey} desconectada para el usuario ${userId}`);
      }

      // Registrar el evento en Firestore para historial
      await db.collection('unipileEvents').add({
        userId,
        accountId: account_id,
        platform: accountTypeKey,
        event: isConnected ? 'connected' : 'disconnected',
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        rawData: webhookData
      });

      console.log('✅ WEBHOOK PROCESADO CORRECTAMENTE');
      console.log('====================================================');
      return NextResponse.json({ success: true });
    } catch (dbError) {
      console.error('❌ ERROR AL ACTUALIZAR FIRESTORE:');
      console.error(dbError);
      console.log('====================================================');
      return NextResponse.json(
        {
          error: 'Error al actualizar la base de datos',
          details: dbError instanceof Error ? dbError.message : String(dbError)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ ERROR PROCESANDO CONEXIÓN DE CUENTA:');
    console.error(error);
    console.log('====================================================');
    return NextResponse.json(
      {
        error: 'Error procesando conexión de cuenta',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}



// Manejar OPTIONS para CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}
