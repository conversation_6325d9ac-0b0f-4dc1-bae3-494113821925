/**
 * Servicio para manejar notificaciones de clientes potenciales
 * Incluye sonido y notificaciones visuales
 */

import { playNotificationSound } from './messageNotifications';
import { createAIClientMatchNotification } from './notifications';
import { NotificationType } from '@/app/types/notifications';

/**
 * Notifica sobre un cliente potencial detectado por la IA
 * Reproduce sonido y crea notificación visual
 */
export const notifyPotentialClient = async (
  userId: string,
  clientName: string,
  clientId: string,
  matchReason: string,
  shouldPlaySound: boolean = true
): Promise<{ success: boolean; notificationId?: string; error?: string }> => {
  try {
    console.log('🔔 Enviando notificación de cliente potencial:', {
      userId,
      clientName,
      matchReason,
      shouldPlaySound
    });

    // Crear la notificación visual
    const notificationId = await createAIClientMatchNotification(
      userId,
      clientName,
      clientId,
      matchReason
    );

    // Reproducir sonido si está habilitado
    if (shouldPlaySound) {
      try {
        console.log('🔊 Reproduciendo sonido para cliente potencial...');
        await playNotificationSound();
        console.log('✅ Sonido de cliente potencial reproducido exitosamente');
      } catch (soundError) {
        console.error('❌ Error al reproducir sonido de cliente potencial:', soundError);
        // No fallar la notificación completa si solo falla el sonido
      }
    } else {
      console.log('🔇 Sonido de cliente potencial deshabilitado por preferencias del usuario');
    }

    return {
      success: true,
      notificationId
    };
  } catch (error) {
    console.error('❌ Error al enviar notificación de cliente potencial:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
};

/**
 * Verifica si el usuario tiene habilitadas las notificaciones de clientes potenciales
 */
export const checkPotentialClientNotificationPreferences = async (userId: string): Promise<boolean> => {
  try {
    // Importar dinámicamente para evitar problemas de SSR
    const { db } = await import('@/lib/firebase');
    const { doc, getDoc } = await import('firebase/firestore');

    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const preferences = userData.notificationPreferences;
      
      if (preferences && preferences.push && typeof preferences.push.potentialClients === 'boolean') {
        return preferences.push.potentialClients;
      }
    }

    // Por defecto, las notificaciones están habilitadas
    return true;
  } catch (error) {
    console.error('Error al verificar preferencias de notificación:', error);
    // En caso de error, asumir que están habilitadas
    return true;
  }
};
