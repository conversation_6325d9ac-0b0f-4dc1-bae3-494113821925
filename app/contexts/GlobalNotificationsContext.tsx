'use client';

import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { useAuth } from './AuthContext';
import { database as rtdb } from '@/lib/firebase/config';
import { ref, onValue, off } from 'firebase/database';
import { notifyNewMessage, updatePageTitle, resetNotifications } from '@/app/services/messageNotifications';
import { useNotificationPreferences } from '@/app/hooks/useNotificationPreferences';

interface GlobalNotificationsContextType {
  unreadMessagesCount: number;
  hasUnreadMessages: boolean;
  resetUnreadCount: () => void;
}

const GlobalNotificationsContext = createContext<GlobalNotificationsContextType | undefined>(undefined);

export function GlobalNotificationsProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const { canReceiveNewMessageNotifications } = useNotificationPreferences();
  const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);
  const previousCountRef = useRef(0);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Inicializar el sistema de notificaciones
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Importar dinámicamente para evitar problemas de SSR
      import('@/app/services/messageNotifications').then(({ initMessageNotifications }) => {
        try {
          initMessageNotifications();
          console.log('✅ Sistema de notificaciones inicializado');
        } catch (error) {
          console.warn('⚠️ No se pudo inicializar el sistema de notificaciones:', error);
        }
      }).catch(error => {
        console.warn('⚠️ Error al importar el servicio de notificaciones:', error);
      });
    }
  }, []);

  // Escuchar cambios en el contador de mensajes no leídos globalmente
  useEffect(() => {
    if (!user?.uid) {
      setUnreadMessagesCount(0);
      try {
        resetNotifications();
      } catch (error) {
        console.warn('⚠️ Error al resetear notificaciones:', error);
      }
      return;
    }

    console.log('🔔 Configurando listener global de notificaciones para usuario:', user.uid);

    // Limpiar listener anterior si existe
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
    }

    // Escuchar todas las conversaciones del usuario
    const conversationsRef = ref(rtdb, `conversations/${user.uid}`);
    
    const unsubscribe = onValue(conversationsRef, (snapshot) => {
      try {
        if (snapshot.exists()) {
          const conversationsData = snapshot.val();
          
          // Calcular el total de mensajes no leídos
          let totalUnread = 0;
          
          Object.entries(conversationsData).forEach(([id, data]: [string, any]) => {
            // Filtrar entradas que no son conversaciones válidas
            if (id !== 'unread' && id !== 'unreadCount' && typeof data === 'object' && data !== null) {
              const unreadCount = data.unreadCount || 0;
              totalUnread += unreadCount;
            }
          });

          console.log('📊 Total de mensajes no leídos:', totalUnread);
          
          // Verificar si el contador aumentó (nuevo mensaje)
          const previousCount = previousCountRef.current;
          const hasNewMessage = totalUnread > previousCount;
          
          // Actualizar el contador
          setUnreadMessagesCount(totalUnread);
          
          // Actualizar el título de la página
          try {
            if (totalUnread > 0) {
              updatePageTitle(totalUnread);
            } else {
              resetNotifications();
            }
          } catch (error) {
            console.warn('⚠️ Error al actualizar título de página:', error);
          }
          
          // Si hay un nuevo mensaje y las notificaciones están habilitadas, reproducir sonido
          if (hasNewMessage && canReceiveNewMessageNotifications && totalUnread > 0) {
            console.log('🔊 Nuevo mensaje detectado globalmente, reproduciendo sonido...');
            // Usar setTimeout para evitar problemas de contexto de audio
            setTimeout(async () => {
              try {
                await notifyNewMessage(totalUnread, false);
                console.log('✅ Notificación global enviada con éxito');
              } catch (error) {
                console.error('❌ Error al enviar notificación global:', error);
              }
            }, 100);
          } else if (hasNewMessage && !canReceiveNewMessageNotifications) {
            console.log('🔇 Nuevo mensaje detectado pero notificaciones deshabilitadas');
          }
          
          // Actualizar la referencia para la próxima comparación
          previousCountRef.current = totalUnread;
        } else {
          console.log('📭 No hay conversaciones');
          setUnreadMessagesCount(0);
          try {
            resetNotifications();
          } catch (error) {
            console.warn('⚠️ Error al resetear notificaciones:', error);
          }
          previousCountRef.current = 0;
        }
      } catch (error) {
        console.error('❌ Error procesando notificaciones globales:', error);
      }
    }, (error) => {
      console.error('❌ Error en listener de notificaciones globales:', error);
    });

    unsubscribeRef.current = unsubscribe;

    // Cleanup
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [user?.uid, canReceiveNewMessageNotifications]);

  const resetUnreadCount = () => {
    setUnreadMessagesCount(0);
    try {
      resetNotifications();
    } catch (error) {
      console.warn('⚠️ Error al resetear notificaciones:', error);
    }
    previousCountRef.current = 0;
  };

  const value = {
    unreadMessagesCount,
    hasUnreadMessages: unreadMessagesCount > 0,
    resetUnreadCount
  };

  return (
    <GlobalNotificationsContext.Provider value={value}>
      {children}
    </GlobalNotificationsContext.Provider>
  );
}

export const useGlobalNotifications = () => {
  const context = useContext(GlobalNotificationsContext);
  if (context === undefined) {
    throw new Error('useGlobalNotifications must be used within a GlobalNotificationsProvider');
  }
  return context;
};
