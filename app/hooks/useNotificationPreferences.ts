'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/app/contexts/AuthContext';
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';

interface NotificationPreferences {
  email: {
    updates: boolean;
    promotions: boolean;
  };
  push: {
    potentialClients: boolean;
    newMessages: boolean;
  };
}

const defaultPreferences: NotificationPreferences = {
  email: {
    updates: true,
    promotions: true,
  },
  push: {
    potentialClients: true,
    newMessages: true,
  },
};

export function useNotificationPreferences() {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPreferences = async () => {
      if (!user?.uid) {
        setIsLoading(false);
        return;
      }

      try {
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.notificationPreferences) {
            setPreferences(userData.notificationPreferences);
          }
        }
      } catch (error) {
        console.error('Error fetching notification preferences:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPreferences();
  }, [user]);

  return {
    preferences,
    isLoading,
    // Funciones de utilidad para acceso rápido
    canReceivePotentialClientNotifications: preferences.push.potentialClients,
    canReceiveNewMessageNotifications: preferences.push.newMessages,
  };
}
