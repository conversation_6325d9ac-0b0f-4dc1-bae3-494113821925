'use client';

import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'sonner';
import { Suspense, lazy, useEffect } from 'react';
import { SocialIntegrationsProvider } from './contexts/SocialIntegrationsContext';
import { TransactionsProvider } from './components/finances/TransactionsContext';
import { ConversationProvider } from './components/conversations/ConversationContext';
import ClientLayout from './ClientLayout';
import { SettingsProvider } from './components/settings/SettingsContext';
import { useAuth } from './contexts/AuthContext';
import ModalProvider from './components/common/ModalProvider';
import { NotificationsProvider } from './contexts/NotificationsContext';
import { GlobalNotificationsProvider } from './contexts/GlobalNotificationsContext';

// Lazy load providers
const ProfileProvider = lazy(() => import('./contexts/ProfileContext').then(mod => ({ default: mod.ProfileProvider })));
const CustomizationProvider = lazy(() => import('./components/customize/CustomizationContext').then(mod => ({ default: mod.CustomizationProvider })));
const LinkProvider = lazy(() => import('./components/links/LinkContext').then(mod => ({ default: mod.LinkProvider })));
const ReviewProvider = lazy(() => import('./components/reviews/ReviewContext').then(mod => ({ default: mod.ReviewProvider })));
const ClientProvider = lazy(() => import('./components/clients/ClientContext').then(mod => ({ default: mod.ClientProvider })));
const AppointmentsProvider = lazy(() => import('./components/appointments/AppointmentsContext').then(mod => ({ default: mod.AppointmentsProvider })));
const TimeOffProvider = lazy(() => import('./components/calendar/TimeOffContext').then(mod => ({ default: mod.TimeOffProvider })));
const FacebookSDKComponent = lazy(() => import('./components/FacebookSDK'));

// Loading fallback component
function LoadingFallback() {
  return null; // o un spinner si lo prefieres
}

// Componente contenedor que solo se renderiza después de que la autenticación esté completa
function AuthDependentProviders({ children }: { children: React.ReactNode }) {
  const { authCheckComplete, loading } = useAuth();

  // Registrar cuándo la autenticación se completa
  useEffect(() => {
    if (authCheckComplete) {
      console.log('🔒 AUTH CHECK COMPLETE - Providers can now initialize');
    }
  }, [authCheckComplete]);

  // Si la autenticación aún no está completa, mostrar pantalla de carga
  if (!authCheckComplete) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-white">
        <div className="text-center">
          <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Cargando...</span>
          </div>
          <p className="mt-3 text-lg font-medium text-gray-800">Iniciando Tatu</p>
          <p className="text-sm text-gray-500">Preparando tu experiencia...</p>
        </div>
      </div>
    );
  }

  // Una vez que la autenticación está completa, renderizar los hijos
  return children;
}

export default function Providers({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} forcedTheme="light">
      <ModalProvider />
      <AuthProvider>
        <AuthDependentProviders>
          <SettingsProvider>
            <SocialIntegrationsProvider>
              <NotificationsProvider>
                <GlobalNotificationsProvider>
                  <Suspense fallback={<LoadingFallback />}>
                  <ProfileProvider>
                    <CustomizationProvider>
                      <LinkProvider>
                        <ReviewProvider>
                          <TransactionsProvider>
                            <ConversationProvider>
                              <ClientProvider>
                                <AppointmentsProvider>
                                  <TimeOffProvider>
                                    <FacebookSDKComponent />
                                    <ClientLayout>
                                      {children}
                                    </ClientLayout>
                                    <Toaster richColors />
                                  </TimeOffProvider>
                                </AppointmentsProvider>
                              </ClientProvider>
                            </ConversationProvider>
                          </TransactionsProvider>
                        </ReviewProvider>
                      </LinkProvider>
                    </CustomizationProvider>
                  </ProfileProvider>
                </Suspense>
                </GlobalNotificationsProvider>
              </NotificationsProvider>
            </SocialIntegrationsProvider>
          </SettingsProvider>
        </AuthDependentProviders>
      </AuthProvider>
    </ThemeProvider>
  );
}
