import { database, db } from '@/lib/firebase/config';
import { ref, push, update, get, set, remove, onValue, off } from 'firebase/database';
import { doc, setDoc, collection, addDoc, Timestamp } from 'firebase/firestore';
import { Notification, NotificationType } from '@/app/types/notifications';
import { v4 as uuidv4 } from 'uuid';

// Referencia a la colección de notificaciones
const getNotificationsRef = (userId: string) => {
  return ref(database, `notifications/${userId}`);
};

// Función de respaldo para crear notificaciones en Firestore
export const createNotificationInFirestore = async (
  userId: string, 
  type: NotificationType, 
  title: string, 
  message: string, 
  metadata?: any
): Promise<string> => {
  try {
    const notificationId = uuidv4();
    const notificationDoc = doc(db, `notifications/${userId}/items`, notificationId);
    
    const notification: Notification = {
      id: notificationId,
      type,
      title,
      message,
      read: false,
      createdAt: Date.now(),
      metadata
    };
    
    await setDoc(notificationDoc, notification);
    console.log('Notificación creada con éxito en Firestore');
    return notificationId;
  } catch (error) {
    console.error('Error creando notificación en Firestore:', error);
    throw error;
  }
};

// Crear una nueva notificación con función de respaldo en Firestore
export const createNotification = async (
  userId: string, 
  type: NotificationType, 
  title: string, 
  message: string, 
  metadata?: any
): Promise<string> => {
  try {
    // Primero intentar con Realtime Database
    const notificationsRef = getNotificationsRef(userId);
    const newNotificationRef = push(notificationsRef);
    
    const notification: Notification = {
      id: newNotificationRef.key || uuidv4(),
      type,
      title,
      message,
      read: false,
      createdAt: Date.now(),
      metadata
    };
    
    try {
      await set(newNotificationRef, notification);
      console.log('Notificación creada con éxito en RTDB');
      return notification.id;
    } catch (rtdbError) {
      console.error('Error creando notificación en RTDB, intentando con Firestore:', rtdbError);
      
      // Si falla RTDB, intentar con Firestore
      return await createNotificationInFirestore(userId, type, title, message, metadata);
    }
  } catch (error) {
    console.error('Error final creando notificación:', error);
    throw error;
  }
};

// Obtener notificaciones de un usuario
export const getNotifications = async (userId: string): Promise<Notification[]> => {
  try {
    const notificationsRef = getNotificationsRef(userId);
    const snapshot = await get(notificationsRef);
    
    if (!snapshot.exists()) {
      return [];
    }
    
    const notifications: Notification[] = [];
    snapshot.forEach((childSnapshot) => {
      notifications.push(childSnapshot.val() as Notification);
    });
    
    // Ordenar por fecha de creación (más reciente primero)
    return notifications.sort((a, b) => b.createdAt - a.createdAt);
  } catch (error) {
    console.error('Error getting notifications:', error);
    return [];
  }
};

// Marcar notificación como leída
export const markNotificationAsRead = async (userId: string, notificationId: string): Promise<void> => {
  try {
    const notificationRef = ref(database, `notifications/${userId}/${notificationId}`);
    await update(notificationRef, { read: true });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

// Marcar todas las notificaciones como leídas
export const markAllNotificationsAsRead = async (userId: string): Promise<void> => {
  try {
    const notifications = await getNotifications(userId);
    const updates: Record<string, any> = {};
    
    notifications.forEach(notification => {
      if (!notification.read) {
        updates[`notifications/${userId}/${notification.id}/read`] = true;
      }
    });
    
    if (Object.keys(updates).length > 0) {
      await update(ref(database), updates);
    }
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

// Eliminar una notificación
export const deleteNotification = async (userId: string, notificationId: string): Promise<void> => {
  try {
    const notificationRef = ref(database, `notifications/${userId}/${notificationId}`);
    await remove(notificationRef);
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};

// Subscribirse a cambios en las notificaciones
export const subscribeToNotifications = (
  userId: string, 
  callback: (notifications: Notification[]) => void
): () => void => {
  const notificationsRef = getNotificationsRef(userId);
  
  const handleValueChange = (snapshot: any) => {
    if (!snapshot.exists()) {
      callback([]);
      return;
    }
    
    const notifications: Notification[] = [];
    snapshot.forEach((childSnapshot: any) => {
      notifications.push(childSnapshot.val() as Notification);
    });
    
    // Ordenar por fecha de creación (más reciente primero)
    callback(notifications.sort((a, b) => b.createdAt - a.createdAt));
  };
  
  onValue(notificationsRef, handleValueChange);
  
  // Retorna función para dejar de escuchar los cambios
  return () => off(notificationsRef, 'value', handleValueChange);
};

// Crear notificación de sesión para hoy
export const createSessionTodayNotification = async (
  userId: string,
  clientName: string,
  sessionTime: string,
  sessionId: string
): Promise<string> => {
  return createNotification(
    userId,
    NotificationType.SESSION_TODAY,
    'Sesión programada para hoy',
    `Tienes una sesión con ${clientName} a las ${sessionTime}`,
    { sessionId }
  );
};

// Crear notificación de cliente potencial detectado por IA
export const createAIClientMatchNotification = async (
  userId: string,
  clientName: string,
  clientId: string,
  matchReason: string
): Promise<string> => {
  return createNotification(
    userId,
    NotificationType.AI_CLIENT_MATCH,
    'Cliente potencial detectado',
    `La IA ha identificado a ${clientName} como un cliente potencial debido a ${matchReason}`,
    { clientId }
  );
}; 