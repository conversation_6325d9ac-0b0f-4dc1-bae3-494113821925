"use client";

import { useEffect, useState } from 'react';
import { useNotifications } from '@/app/contexts/NotificationsContext';
import { Notification, NotificationType } from '@/app/types/notifications';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { useRouter } from 'next/navigation';

export default function NotificationsCenter() {
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Evitar errores de hidratación
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Notificaciones</h2>
          <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
            Cargando...
          </span>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-16 bg-gray-100 rounded-lg"></div>
          <div className="h-16 bg-gray-100 rounded-lg"></div>
        </div>
      </div>
    );
  }

  const formatTime = (timestamp: number) => {
    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: es
      });
    } catch (error) {
      return 'fecha desconocida';
    }
  };

  const getNotificationPriority = (type: NotificationType): 'high' | 'normal' => {
    switch (type) {
      case NotificationType.SESSION_TODAY:
        return 'high';
      default:
        return 'normal';
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);

    // Navegar según el tipo de notificación
    if (notification.type === NotificationType.SESSION_TODAY && notification.metadata?.sessionId) {
      router.push(`/calendar?sessionId=${notification.metadata.sessionId}`);
    } else if (notification.type === NotificationType.AI_CLIENT_MATCH) {
      // Redireccionar a la página de mensajes si hay un conversationId
      if (notification.metadata?.conversationId) {
        router.push(`/messages?conversation=${notification.metadata.conversationId}`);
      } else {
        // Si no hay conversationId, intentar usar el clientId como respaldo
        console.log('No se encontró conversationId en la notificación, usando clientId como respaldo');
        router.push(`/messages`);
      }
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Notificaciones</h2>
        {unreadCount > 0 ? (
          <div className="flex items-center gap-4">
            <button 
              onClick={() => markAllAsRead()}
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              Marcar todas como leídas
            </button>
            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm">
              {unreadCount} sin leer
            </span>
          </div>
        ) : (
          <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
            No hay nuevas
          </span>
        )}
      </div>

      <div className="space-y-4 max-h-[400px] overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No tienes notificaciones</p>
          </div>
        ) : (
          notifications.slice(0, 5).map((notification) => {
            const priority = getNotificationPriority(notification.type);
            const isUnread = !notification.read;
            
            return (
              <button
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                className={`w-full text-left p-4 rounded-lg transition-colors ${
                  priority === "high" ? "bg-red-50 hover:bg-red-100" : "bg-gray-50 hover:bg-gray-100"
                } ${isUnread ? 'border-l-4 border-primary-500' : ''}`}
              >
                <div className="flex justify-between">
                  <h3 className={`font-medium ${isUnread ? 'text-gray-900' : 'text-gray-700'}`}>
                    {notification.title}
                  </h3>
                  <span className="text-sm text-gray-500">{formatTime(notification.createdAt)}</span>
                </div>
                <p className={`mt-1 ${isUnread ? 'text-gray-600' : 'text-gray-500'}`}>
                  {notification.message}
                </p>
              </button>
            );
          })
        )}
      </div>
      
      {notifications.length > 5 && (
        <div className="mt-4 text-center">
          <button 
            onClick={() => router.push('/notifications')}
            className="text-sm text-primary-600 hover:text-primary-800"
          >
            Ver todas las notificaciones
          </button>
        </div>
      )}
    </div>
  );
}
